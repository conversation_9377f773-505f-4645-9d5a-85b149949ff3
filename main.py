import uvicorn
from fastapi import Fast<PERSON><PERSON>
from starlette.middleware.cors import CORSMiddleware

from app import api

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api.router)

if __name__ == "__main__":
    # 启动 FastAPI 服务
    uvicorn.run("main:app", host="127.0.0.1", port=8080, reload=True)