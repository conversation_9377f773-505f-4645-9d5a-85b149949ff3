from fastapi import APIRouter

from app.schemas.auth import LoginReq
from app.schemas.common import success, failure, Err

router = APIRouter(prefix="/auth", tags=["鉴权"])

@router.post('/login', summary='用户登录')
def login(req: LoginReq):
    if req.username == "admin" and req.password == "123456":
        return success(data={
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjpbeyJ1c2VyTmFtZSI6IlNveWJlYW4ifV0sImlhdCI6MTY5ODQ4NDg2MywiZXhwIjoxNzMwMDQ0Nzk5LCJhdWQiOiJzb3liZWFuLWFkbWluIiwiaXNzIjoiU295YmVhbiIsInN1YiI6IlNveWJlYW4ifQ._w5wmPm6HVJc5fzkSrd_j-92d5PBRzWUfnrTF1bAmfk",
            "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjpbeyJ1c2VyTmFtZSI6IlNveWJlYW4ifV0sImlhdCI6MTY5ODQ4NDg4MSwiZXhwIjoxNzYxNTgwNzk5LCJhdWQiOiJzb3liZWFuLWFkbWluIiwiaXNzIjoiU295YmVhbiIsInN1YiI6IlNveWJlYW4ifQ.7dmgo1syEwEV4vaBf9k2oaxU6IZVgD2Ls7JK1p27STE"
        })
    else:
        return failure(Err.LOGIN_FAILURE)

@router.post('/getUserInfo', summary='获取当前用户信息')
def getUserInfo():
    return success(data={
		"userId": "0",
		"username": "admin",
        "roles": [],
    })