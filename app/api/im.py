from fastapi import APIRouter

from app.enums.enums import Modality, Organ
from app.schemas.common import success
from app.schemas.im import AnnotateReq
from app.services import im

router = APIRouter(prefix="/im", tags=["影像学"])

@router.post('/annotate', summary='打标签')
def annotate(req: AnnotateReq):
    if req.streaming:
        return None
    else:
        feature = im.annotate(Modality.ULTRASOUND, Organ.THYROID, req.report_text)
        return success(data=feature)
