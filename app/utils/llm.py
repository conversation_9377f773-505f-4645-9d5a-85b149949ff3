import os
from enum import Enum

from langchain_openai import ChatOpenAI
from pydantic import SecretStr


def init_langsmith(project_name: str):
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
    os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGCHAIN_API_KEY")
    os.environ["LANGCHAIN_PROJECT"] = project_name

local = ChatOpenAI(
    model="gpt-4o",
    base_url="http://192.168.1.12:11435/v1/",
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    temperature=0.6
)

local_no_think = ChatOpenAI(
    model="gpt-4o",
    base_url="http://192.168.1.12:11435/v1/",
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    temperature=0.6,
    extra_body={"chat_template_kwargs": {"enable_thinking": False}},
)

local_fd = ChatOpenAI(
    model="gpt-4o",
    base_url = "http://192.168.1.12:8868/v1",
    api_key=SecretStr("zjTi%9N^fqWQGh"),
    temperature=0.6
)

local_lb = ChatOpenAI(
    model="gpt-4o",
    base_url="http://192.168.1.12:8068/v1",
    api_key=SecretStr("zjTi%9N^fqWQGh"),
    temperature=0.6
)

remote_12 = ChatOpenAI(
    model="gpt-4o",
    base_url="http://100.103.83.39:10023/v1/",
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    temperature=0.6
)

class Qwen(str, Enum):
    QWEN3_14B = "qwen3-14b"
    QWEN3_235B_A22B = "qwen3-235b-a22b"
    QWEN3_30B_A3B_INSTRUCT_2507 = "qwen3-30b-a3b-instruct-2507"

def llm_qwen3(model: Qwen, streaming):
    llm = ChatOpenAI(
        model=model,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        streaming=streaming,
        temperature=0.6,
    )
    if not streaming:
        llm.extra_body = {"enable_thinking": False}
    return llm

def get_llm(streaming: bool = False):
    return llm_qwen3(Qwen.QWEN3_235B_A22B, streaming)
    # return local