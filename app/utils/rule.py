def extract_leaf_values(feature: dict):
    values = []

    def _walk(node):
        if isinstance(node, dict):
            for k, v in node.items():
                _walk(v)
        elif isinstance(node, list):
            for item in node:
                _walk(item)
        else:
            values.append(node)

    _walk(feature)
    return values

def extract_leaf_kv(feature: dict):
    values = []

    def _walk(node, path=""):
        if isinstance(node, dict):
            for k, v in node.items():
                new_path = f"{path}.{k}" if path else k
                _walk(v, new_path)
        else:
            # 直接将所有非字典节点（包括数组和叶节点值）作为值保存
            values.append({
                "path": path,
                "value": node
            })

    _walk(feature)
    return values

if __name__ == "__main__":
    d = {"structure": {"capsule": {"完整性": ["甲状腺包膜清晰完整", "甲状腺包膜不完整"]}, "lymph_node": {"可见度": "甲状腺周围未见明显形态饱满淋巴结"}, "parenchyma": {"大小": "甲状腺大小正常", "形状": "甲状腺形状规则", "光滑度": "甲状腺表面光滑", "对称性": "甲状腺形态对称", "切面形态": "甲状腺切面形态正常", "回声特征": "甲状腺回声正常"}, "blood_vessel": {}}}

    print(extract_leaf_values(d))
    print(extract_leaf_kv(d))