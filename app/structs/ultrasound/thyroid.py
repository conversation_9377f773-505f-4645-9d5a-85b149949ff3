from typing import List, Optional

from langchain_core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field

DESCRIPTION = "只能从以下选项中选择："


class Parenchyma(BaseModel):
    大小: Optional[str] = Field(None, title="大小", description=f"甲状腺大小正常：包含“体积不大”。{DESCRIPTION} 甲状腺大小正常、甲状腺增大、甲状腺萎缩")
    活跃度: Optional[str] = Field(None, title="活跃度", description=f"{DESCRIPTION} 甲状腺活动度正常、甲状腺活动度异常")
    回声特征: Optional[str] = Field(None, title="回声特征", description=f"甲状腺回声正常：包含“甲状腺回声光点细密均匀”。{DESCRIPTION} 甲状腺回声正常")
    切面形态: Optional[str] = Field(None, title="切面形态", description=f"{DESCRIPTION} 甲状腺切面形态正常、甲状腺切面形态失常 甲状腺切面大小正常、甲状腺切面增大、甲状腺切面减小")
    对称性: Optional[str] = Field(None, title="对称性", description=f"{DESCRIPTION} 甲状腺形态对称、甲状腺形态不对称")
    形状: Optional[str] = Field(None, title="形状", description=f"{DESCRIPTION} 甲状腺形状规则、甲状腺形状不规则")
    光滑度: Optional[str] = Field(None, title="光滑度", description=f"{DESCRIPTION} 甲状腺表面光滑、甲状腺表面粗糙")
    血流信号: Optional[str] = Field(None, title="血流信号", description=f"{DESCRIPTION} 甲状腺血流信号增加、甲状腺血流信号减少、甲状腺血流信号正常")
    术后状态: Optional[str] = Field(None, title="术后状态", description=f"{DESCRIPTION} 甲状腺癌术后、甲状腺部分切除术后、甲状腺全切术后、甲状腺消融术后、甲状腺癌消融术后、甲状腺囊肿切除术后、甲状腺术后、甲状腺术后可能、甲状腺术后改变、甲状腺根治术后、甲状腺结节切除术后、甲状腺肿块切除术后、甲状腺结节剔除术后、甲状腺腺瘤剔除术后")

class Capsule(BaseModel):
    完整性: Optional[str] = Field(None, title="完整性", description=f"{DESCRIPTION} 甲状腺包膜清晰完整、甲状腺包膜模糊、甲状腺包膜中断")

class BloodVessel(BaseModel):
    走行: Optional[str] = Field(None, title="走行", description=f"{DESCRIPTION} 甲状腺血管走行清晰、甲状腺血管走行模糊")

class LymphNode(BaseModel):
    可见度: Optional[str] = Field(None, title="可见度", description=f"{DESCRIPTION} 甲状腺周围可见明显形态饱满淋巴结、甲状腺周围未见明显形态饱满淋巴结")

class Structure(BaseModel):
    甲状腺本体: Optional[Parenchyma] = Field(None, title="甲状腺本体")
    包膜: Optional[Capsule] = Field(None, title="包膜")
    血管: Optional[BloodVessel] = Field(None, title="血管")
    周围淋巴结: Optional[LymphNode] = Field(None, title="周围淋巴结")

class DiffuseChanges(BaseModel):
    # 发现区域: str = Field(..., title="区域", description=f"{DESCRIPTION} 甲状腺")
    回声强度: Optional[str] = Field(None, title="回声强度", description=f"{DESCRIPTION} 无回声、极低回声、低回声、等回声、高回声")
    回声分布: Optional[str] = Field(None, title="回声分布", description=f"{DESCRIPTION} 弥漫性")
    回声纹理特征: Optional[List[str]] = Field(None, title="回声纹理特征", description=f"可多选，{DESCRIPTION} 回声光点增粗、回声光点稀疏、回声光点细密、网格状回声")
    回声均匀性: Optional[str] = Field(None, title="回声均匀性", description=f"{DESCRIPTION} 均匀、不均匀")

class FocalLesion(BaseModel):
    发现部位: str = Field(..., title="区域", description=f"{DESCRIPTION} 甲状腺左叶、甲状腺右叶、甲状腺峡部、甲状腺")
    回声强度: Optional[str] = Field(None, title="回声强度", description=f"低回声：包含“稍低回声”。{DESCRIPTION} 无回声、极低回声、低回声、等回声、高回声、异常回声")
    回声分布: Optional[str] = Field(None, title="回声分布", description=f"{DESCRIPTION} 局限性")
    回声均匀性: Optional[str] = Field(None, title="回声均匀性", description=f"{DESCRIPTION} 均匀、不均匀")
    内部成分: Optional[List[str]] = Field(None, title="内部成分", description=f"囊实性：包含“混合性”。可多选，{DESCRIPTION} 囊实性、囊性、实性、粗大钙化、周边钙化、内部点状强回声")
    回声纹理特征: Optional[List[str]] = Field(None, title="回声纹理特征", description=f"可多选，{DESCRIPTION} 回声光点稀疏、回声光点细密、散在微小光点")
    回声形态: Optional[str] = Field(None, title="回声形态", description=f"{DESCRIPTION} 点状、斑状、团状、环状、带状")
    后方回声特征: Optional[List[str]] = Field(None, title="后方回声特征", description=f"{DESCRIPTION} 增强、衰减、无改变、混合性改变、伴声影、不伴声影")
    异常征象: Optional[str] = Field(None, title="异常征象", description=f"{DESCRIPTION} 彗星尾征、海绵征")
    声晕: Optional[str] = Field(None, title="声晕", description=f"{DESCRIPTION}  周边声晕完整、周边声晕不完整、周边声晕均匀、周边声晕不均匀、周边声晕厚、周边声晕薄")
    边缘特征: Optional[List[str]] = Field(None, title="边缘特征", description=f"{DESCRIPTION} 清晰、模糊、分叶状、成角状、毛刺状、光滑、不规则状、甲状腺外侵犯")
    血流信号: Optional[str] = Field(None, title="血流信号", description=f"{DESCRIPTION}  血流信号正常、无血流信号、血流信号稀少、血流信号丰富")
    大小: Optional[str] = Field(None, title="大小", description=f"小：直径小于1cm、中：直径为1cm-2cm、大：直径大于2cm。{DESCRIPTION} 小、中、大")
    纵横比: Optional[str] = Field(None, title="纵横比", description=f"纵横比小于1：横径*纵径，纵径/横径小于1、纵横比等于1：横径*纵径，纵径/横径等于1、纵横比大于1：横径*纵径，纵径/横径大于1。{DESCRIPTION} 纵横比小于1、纵横比等于1、纵横比大于1")
    数量: Optional[str] = Field(None, title="数量", description=f"{DESCRIPTION} 单发、多发")
    形状: Optional[str] = Field(None, title="形状", description=f"{DESCRIPTION} 椭圆形、圆形、直立状、规则形、不规则形")

class Thyroid(BaseModel):
    structure: Optional[Structure] = Field(None, title="解剖实体")
    diffuse_changes: Optional[List[DiffuseChanges]] = Field(None, title="弥漫性改变")
    focal_lesions: Optional[List[FocalLesion]] = Field(None, title="局灶性病灶")

if __name__ == "__main__":
    print(PydanticOutputParser(pydantic_object=Thyroid).get_format_instructions())