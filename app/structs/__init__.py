from app.structs.ultrasound import ultrasound_registry

MODEL_REGISTRY = {
    "ultrasound": ultrasound_registry,
}

def get_model_class(modality: str, organ: str):
    modality_dict = MODEL_REGISTRY.get(modality.lower())
    if not modality_dict:
        raise ValueError(f"暂不支持 modality: {modality}")
    model_class = modality_dict.get(organ.lower())
    if not model_class:
        raise ValueError(f"暂不支持 organ: {organ} under modality: {modality}")
    return model_class