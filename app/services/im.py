from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from app.enums.enums import Modality, Organ
from app.structs import get_model_class
from app.utils.llm import get_llm, init_langsmith
from app.utils.rule import extract_leaf_values, extract_leaf_kv

template = """
你是一名资深的医生助理。请根据提供的超声影像学报告，提取符合原文中明确表述的甲状腺结构变化或病灶信息。

必须严格遵守以下要求：
1. **禁止基于推理、联想或常识扩展内容**，仅根据报告中“明确出现”的词句提取；
2. **判断是否存在“弥漫性病变”**：仅当报告中出现整体描述如“实质回声不均”“弥漫分布”“滤泡状”“低回声弥漫改变”等词句时，才提取 diffuse_changes；
3. **判断是否存在“局灶性病变”**：仅当报告中提及“低回声区”“结节”“肿块”“占位”等病灶明确特征时提取 focal_lesions；

影像学报告: {report_text}

{format_instructions}
"""

def annotate(modality, organ, report_text: str):
    model_class = get_model_class(modality, organ)
    parser = JsonOutputParser(pydantic_object=model_class)
    prompt = PromptTemplate(
        template=template,
        input_variables=["report_text"],
        partial_variables={"format_instructions": parser.get_format_instructions()},
    )
    chain = prompt | get_llm() | parser
    return chain.invoke({"report_text": report_text})

def annotate_extract(feature: dict):
    return {
        "structure": extract_leaf_values(feature.get("structure", {})),
        "diffuse_changes": feature.get("diffuse_changes", {}),
        "focal_lesion": feature.get("focal_lesion", [])
    }


if __name__ == "__main__":
    init_langsmith("imagin")
    print(annotate(Modality.ULTRASOUND, Organ.THYROID, "甲状腺双侧叶及峡部切面形态正常,体积不大,表面光滑,包膜完整,内部回声不均,其内左侧叶可见二个稍低回声,大者约4*3mm，右侧叶可见几个稍低回声,大者约11*6mm，形状呈圆形,内回声欠均匀,边界清楚,光滑完整,后方回声无衰减。余甲状腺光点细密均匀，血管走行清晰。彩色多普勒超声示：未见明显异常彩色血流信号。 甲状腺周围、双侧颈部大血管旁未见明显形态饱满淋巴结。"))