from app.enums.enums import TiRads
from app.services.rules.schemas import DxLabel, Extra


def is_cyst(lesion: dict):
    """ 判断 甲状腺囊肿 """
    features = [f"发现部位：{lesion.get("发现部位", "")}"]

    # 规则 1：内部成分 = 囊性
    if "囊性" in lesion.get("内部成分", []):
        features.append("内部成分：囊性")
        return DxLabel(name="甲状腺囊肿", features=features)

    # 规则 2: 回声强度 = 无回声 + 任一特征匹配：
    #   - 边缘特征 = 清晰 / 光滑
    #   - 形状 = 圆形 / 椭圆形
    #   - 后方回声特征 = 增强
    #   - 血流信号 = 无血流信号
    if lesion.get("回声强度", "") == "无回声":
        features.append("回声强度：无回声")

        # - 边缘特征 = 清晰 / 光滑
        intersection = list(set(lesion.get("边缘特征", [])) & {"清晰", "光滑"})
        for item in intersection:
            features.append(f"边缘特征：{item}")
            return DxLabel(name="甲状腺囊肿", features=features[:1])

        # - 形状 = 圆形 / 椭圆形
        if lesion.get("形状", "") in ["圆形", "椭圆形"]:
            features.append(f"形状：{lesion.get("形状")}")
            return DxLabel(name="甲状腺囊肿", features=features)

        #   - 后方回声特征 = 增强
        if "增强" in lesion.get("后方回声特征", []):
            features.append("后方回声特征：增强")
            return DxLabel(name="甲状腺囊肿", features=features)

        #   - 血流信号 = 无血流信号
        if lesion.get("血流信号", "") == "无血流信号":
            features.append("血流信号：无血流信号")
            return DxLabel(name="甲状腺囊肿", features=features)

    return None

def is_nodule(lesion: dict):
    """判断是否为结节：
    若 回声强度 有值 或 回声分布 = 局限性 且 满足以下任一条件：
      1. 存在以下任一特征：内部成分、声晕、边缘特征、纵横比
      2. 异常征象 = 海绵征
    """
    features = [f"发现部位：{lesion.get("发现部位", "")}"]

    is_focal_echo = False
    if lesion.get("回声分布"):
        features.append(f"回声分布：{lesion.get('回声分布')}")
        is_focal_echo = True
    if lesion.get("回声强度") == "局限性":
        features.append(f"回声强度：局限性")
        is_focal_echo = True

    if not is_focal_echo:
        return False

    if len(lesion.get("内部成分", [])) > 1:
        features.append(f"内部成分：{lesion.get("内部成分")[:1]}")
        return DxLabel(name="甲状腺结节", features=features)

    if lesion.get("声晕"):
        features.append(f"声晕：{lesion.get('声晕')}")
        return DxLabel(name="甲状腺结节", features=features)

    if len(lesion.get("边缘特征", [])) > 1:
        features.append(f"边缘特征：{lesion.get("边缘特征")[:1]}")
        return DxLabel(name="甲状腺结节", features=features)

    if lesion.get("纵横比"):
        features.append(f"纵横比：{lesion.get('纵横比')}")
        return DxLabel(name="甲状腺结节", features=features)

    if lesion.get("异常征象", "") == "海绵征":
        features.append(f"异常征象：海绵征")
        return DxLabel(name="甲状腺结节", features=features)

    return None

def is_mass(lesion: dict):
    """
    甲状腺占位性病变（满足≥1条，有值即可）
    1、[局限性病变-回声分布-局限性]
    2、[局限性病变-内部成分]
    3、[局限性病变-后方回声特征]
    4、[局限性病变-异常征象]
    5、[局限性病变-声晕]
    6、[局限性病变-边缘特征]
    7、[局限性病变-回声强度]+[局限性病变-大小]
    8、[局限性病变-纵横比]
    9、[局限性病变-形状]
    """
    features = [f"发现部位：{lesion.get("发现部位", "")}"]
    if lesion.get("回声分布", "") == "局限性":
        features.append("回声分布：局限性")
        return DxLabel(name="甲状腺占位性病变", features=features)
    if len(lesion.get("内部成分", [])) > 1:
        features.append(f"内部成分：{lesion.get("内部成分")[:1]}")
        return DxLabel(name="甲状腺占位性病变", features=features)
    if len(lesion.get("后方回声特征", [])) > 1:
        features.append(f"后方回声特征：{lesion.get("后方回声特征")[:1]}")
        return DxLabel(name="甲状腺占位性病变", features=features)
    if lesion.get("异常征象"):
        features.append(f"异常征象：{lesion.get("异常征象")}")
        return DxLabel(name="甲状腺占位性病变", features=features)
    if lesion.get("声晕"):
        features.append(f"声晕：{lesion.get("声晕")}")
        return DxLabel(name="甲状腺占位性病变", features=features)
    if len(lesion.get("边缘特征", [])) > 1:
        features.append(f"边缘特征：{lesion.get("边缘特征")[:1]}")
        return DxLabel(name="甲状腺占位性病变", features=features)
    if lesion.get("回声强度") and lesion.get("大小"):
        features.append(f"回声强度：{lesion.get("回声强度")}")
        features.append(f"大小：{lesion.get("大小")}")
        return DxLabel(name="甲状腺占位性病变", features=features)
    if lesion.get("纵横比"):
        features.append(f"纵横比：{lesion.get("纵横比")}")
        return DxLabel(name="甲状腺占位性病变", features=features)
    if lesion.get("形状"):
        features.append(f"形状：{lesion.get("形状")}")
        return DxLabel(name="甲状腺占位性病变", features=features)
    return None

def calc_composition(features: list[str]) -> (int, str):
    if features and len(features) > 0:
        return 1, "实性"
    return 0, ""

def calc_ti_rads(lesion: dict) -> Extra:
    pass

def validate(feature: dict):
    labels = []
    if feature is None:
        return labels
    lesions = feature.get("focal_lesions", [])

    for lesion in lesions:
        label = is_cyst(lesion)
        if label:
            label.extra = Extra(name=TiRads.TI_RADS_1, features=[])
            labels.append(label)
            continue
        label = is_nodule(lesion)
        if label:
            label.extra = calc_ti_rads(lesion)
            labels.append(label)
            continue
        label = is_mass(lesion)
        if label:
            label.extra = calc_ti_rads(lesion)
            labels.append(label)
            continue
    return labels