from app.services.rules.schemas import DxLabel
from app.utils.rule import extract_leaf_values


def validate(feature: dict):

    if feature is None:
        return None

    structure_values = extract_leaf_values(feature)

    focal_lesions = feature.get("focal_lesions", [])
    diffuse_changes = feature.get("diffuse_changes", [])

    # 1、出现弥漫性病变异常（满足≥1条）
    #   1)弥漫性病变-回声均匀性=不均匀
    #   2)解剖实体-甲状腺腺体-切面形态=甲状腺切面形态失常/甲状腺切面增大/甲状腺切面减小
    #   3)解剖实体-甲状腺腺体-对称性=甲状腺形态不对称
    #   4)解剖实体-甲状腺腺体-形状=甲状腺形状不规则
    # 2、无局灶性病变
    #   局限性病变=空
    if len(focal_lesions) == 0:
        # 1. 基于结构信息的匹配
        rule_values = ["甲状腺切面形态失常", "甲状腺切面增大", "甲状腺切面减小", "甲状腺形态不对称", "甲状腺形状不规则"]
        features = list(set(rule_values) & set(structure_values))
        if len(features) > 0:
            return DxLabel(name="甲状腺弥漫性病变", features=features[:1])

        # 2. 弥漫性病变判断
        for dc in diffuse_changes:
            if dc.get("回声均匀性", "") == "不均匀":
                return DxLabel(name="甲状腺弥漫性病变", features=["【弥漫性病变】回声均匀性：不均匀"])

    else:
        rule_values = ["甲状腺增大", "甲状腺萎缩", "甲状腺表面粗糙"]
        features = list(set(rule_values) & set(structure_values))
        if len(features) > 0:
            return DxLabel(name="甲状腺弥漫性病变", features=features[:1])

        for dc in diffuse_changes:
            if dc.get("回声均匀性", "") == "不均匀":
                intersection_items = list(set(dc.get("回声纹理特征", [])) & {"回声光点增粗", "网格状回声"})
                if len(intersection_items) > 0:
                    for item in intersection_items:
                        return DxLabel(name="甲状腺弥漫性病变", features=[f"【弥漫性病变】回声纹理特征：{item}"])

            if dc.get("回声分布", "") == "弥漫性":
                return DxLabel(name="甲状腺弥漫性病变", features=[f"【弥漫性病变】回声分布：弥漫性"])
    return None