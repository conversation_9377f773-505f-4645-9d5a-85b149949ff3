from enum import Enum
from typing import Optional, Any

from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel
from starlette import status
from starlette.responses import JSONResponse

class Response(BaseModel):
    code: str = "200"
    message: str = "ok"
    data: Optional[Any] = None
    total: Optional[int] = None
    errors: Optional[Any] = None

class Err(tuple, Enum):
    HTTP_400 = ("400", "BadRequest")
    HTTP_401 = ("401", "请登录")
    HTTP_404 = ("404", "NotFound")
    HTTP_500 = ("500", "InternalServerError")
    CUSTOM = ("999", "业务错误")
    LOGIN_FAILURE = ("login_failure", "用户名或密码错误")
    USER_INACTIVE = ("user_inactive", "用户已失效或不存在")


def success(data: Any = None, total: int = None):
    return Response(
        data=data,
        total=total,
    ).model_dump(exclude_none=True)


def failure(err: Err, errors: Any = None):
    message = errors if err == Err.CUSTOM else err[1]
    return Response(
        code=err[0],
        message=message,
        errors=errors
    ).model_dump(exclude_none=True)


def response400(exc: RequestValidationError):
    return JSONResponse(
        headers={'Access-Control-Allow-Origin': '*'},
        status_code=status.HTTP_200_OK,
        content=failure(Err.HTTP_400, exc.errors())
    )


def response401():
    return JSONResponse(
        headers={'Access-Control-Allow-Origin': '*'},
        status_code=status.HTTP_200_OK,
        content=failure(Err.HTTP_401)
    )


def response404():
    return JSONResponse(
        headers={'Access-Control-Allow-Origin': '*'},
        status_code=status.HTTP_200_OK,
        content=failure(Err.HTTP_404)
    )


def response500():
    return JSONResponse(
        headers={'Access-Control-Allow-Origin': '*'},
        status_code=status.HTTP_200_OK,
        content=failure(Err.HTTP_500)
    )