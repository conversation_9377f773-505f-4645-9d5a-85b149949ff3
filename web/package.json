{"name": "soybean-admin-react", "type": "module", "version": "2.0.0", "private": false, "packageManager": "pnpm@10.4.1", "description": "A fresh and elegant admin template, based on React18、Vite5、TypeScript、Ant Design and UnoCSS. 一个基于React18、Vite5、TypeScript、Ant Design and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Ohh", "email": "<EMAIL>", "url": "https://github.com/mufeng889"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin-react", "repository": {"url": "https://github.com/soybeanjs/soybean-admin-react.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin-react/issues"}, "keywords": ["React admin", "react-admin-template", "Vite5", "TypeScript", "Ant Design", "antd-admin", "Redux", "React-Router V6", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "1.0.3", "@better-scroll/core": "2.5.1", "@dnd-kit/core": "6.3.1", "@dnd-kit/sortable": "10.0.0", "@iconify/react": "5.2.0", "@reduxjs/toolkit": "2.5.1", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@types/react-syntax-highlighter": "^15.5.13", "ahooks": "3.8.4", "antd": "5.24.1", "clsx": "2.1.1", "dayjs": "1.11.13", "echarts": "5.6.0", "i18next": "24.2.2", "keepalive-for-react": "4.0.0", "lodash-es": "4.17.21", "motion": "12.4.7", "nprogress": "0.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-error-boundary": "5.0.0", "react-i18next": "15.4.1", "react-redux": "9.2.0", "react-router-dom": "7.2.0", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@iconify/json": "2.2.309", "@iconify/types": "2.0.0", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybean-react/vite-plugin-react-router": "0.7.1", "@soybeanjs/eslint-config": "1.5.3", "@svgr/core": "8.1.0", "@svgr/plugin-jsx": "8.1.0", "@types/gradient-string": "1.1.6", "@types/lodash-es": "4.17.12", "@types/node": "22.13.5", "@types/nprogress": "0.2.3", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/react-transition-group": "4.4.12", "@typescript-eslint/eslint-plugin": "8.24.1", "@typescript-eslint/parser": "8.24.1", "@unocss/eslint-config": "66.0.0", "@unocss/preset-icons": "66.0.0", "@unocss/preset-uno": "66.0.0", "@unocss/transformer-directives": "66.0.0", "@unocss/transformer-variant-group": "66.0.0", "@unocss/vite": "66.0.0", "boxen": "8.0.1", "consola": "3.4.0", "eslint": "9.21.0", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.1.0", "eslint-plugin-react-refresh": "0.4.19", "eslint-plugin-sort": "4.0.0", "gradient-string": "3.0.0", "json5": "2.2.3", "kolorist": "1.8.0", "lint-staged": "15.4.3", "sass": "1.85.0", "simple-git-hooks": "2.11.1", "tsx": "4.19.3", "typescript": "5.7.3", "unplugin-auto-import": "19.1.0", "unplugin-icons": "22.1.0", "vite": "6.1.1", "vite-plugin-inspect": "10.2.1", "vite-plugin-remove-console": "2.2.0", "vite-plugin-svg-icons": "2.0.1"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://react.soybeanjs.cn/home/"}