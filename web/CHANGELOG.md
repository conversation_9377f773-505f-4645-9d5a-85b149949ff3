# Changelog


## [v2.0.0](https://github.com/soybeanjs/soybean-admin-react/compare/v1.2.0...v2.0.0) (2025-03-20)

### &nbsp;&nbsp;&nbsp;🚀 Features

- 安装 剪切板 antd19补丁依赖 更新@soybean-react/vite-plugin-react-router 删除typeit react-countup react-beatiful-dnd &nbsp;-&nbsp; by **wang** [<samp>(c77fb)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c77fb9a)
- 添加菜单布局的常量 &nbsp;-&nbsp; by **wang** [<samp>(47446)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/4744640)
- 添加antd相关的配置文件 &nbsp;-&nbsp; by **wang** [<samp>(e2c50)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/e2c50d5)
- 在features中集成 router &nbsp;-&nbsp; by **wang** [<samp>(4ccb5)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/4ccb5aa)
- 集成全局侧边栏配置 &nbsp;-&nbsp; by **wang** [<samp>(a66bb)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a66bb7f)
- 集成全局搜索 &nbsp;-&nbsp; by **wang** [<samp>(923cb)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/923cb3f)
- 集成全局菜单 &nbsp;-&nbsp; by **wang** [<samp>(d2111)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d211150)
- 集成全局Content &nbsp;-&nbsp; by **wang** [<samp>(24a1f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/24a1f6f)
- 增加新的路由页面 &nbsp;-&nbsp; by **wang** [<samp>(d0301)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d0301d7)
- 添加 BetondHiding组件 &nbsp;-&nbsp; by **wang** [<samp>(abb8d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/abb8df7)
- 添加filpText组件 &nbsp;-&nbsp; by **wang** [<samp>(066b2)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/066b232)
- 添加DarkModeContainer组件 &nbsp;-&nbsp; by **wang** [<samp>(30487)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3048795)
- 添加FullScreen组件 &nbsp;-&nbsp; by **wang** [<samp>(08dc1)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/08dc169)
- 添加lookForward组件 &nbsp;-&nbsp; by **wang** [<samp>(7176f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7176f01)
- 添加PinToggler组件 &nbsp;-&nbsp; by **wang** [<samp>(3dcf7)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3dcf746)
- 集成menu &nbsp;-&nbsp; by **wang** [<samp>(5e8a8)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5e8a8cf)
- 集成router &nbsp;-&nbsp; by **wang** [<samp>(9e824)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/9e824da)
- 添加BaseLayout &nbsp;-&nbsp; by **wang** [<samp>(3c094)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3c09412)
- 添加GlobalLogo &nbsp;-&nbsp; by **wang** [<samp>(81cc1)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/81cc1cc)
- 添加全局GlobalSider &nbsp;-&nbsp; by **wang** [<samp>(d54de)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d54ded1)
- 添加头部主题按钮组件 &nbsp;-&nbsp; by **wang** [<samp>(a0b77)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a0b77f1)
- 添加全局GlobalHeader组件 &nbsp;-&nbsp; by **wang** [<samp>(a2d97)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a2d9782)
- 集成演示home页面 &nbsp;-&nbsp; by **wang** [<samp>(bd08f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/bd08fba)
- 集成个人中心页面 &nbsp;-&nbsp; by **wang** [<samp>(f1424)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f142401)
- 集成多级菜单页面 &nbsp;-&nbsp; by **wang** [<samp>(7a3f3)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7a3f339)
- 添加头像组件 &nbsp;-&nbsp; by **wang** [<samp>(64fae)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/64faefe)
- 添加NumberTicker组件 &nbsp;-&nbsp; by **wang** [<samp>(2f286)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/2f2865e)
- 添加tab的相关store &nbsp;-&nbsp; by **wang** [<samp>(5c696)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5c696fc)
- 添加route的相关store &nbsp;-&nbsp; by **wang** [<samp>(5ed2b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5ed2bed)
- 添加初始化缓存路由的相关代码 &nbsp;-&nbsp; by **wang** [<samp>(10b29)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/10b2988)
- 添加useThemeSettings &nbsp;-&nbsp; by **wang** [<samp>(3fa8f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3fa8f2a)
- 添加ExceptionBase组件 &nbsp;-&nbsp; by **wang** [<samp>(c924d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c924dc9)
- 集成相关的auth &nbsp;-&nbsp; by **wang** [<samp>(15a59)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/15a5995)
- 添加全局tab &nbsp;-&nbsp; by **wang** [<samp>(aa15b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/aa15bfc)
- 添加manage相关的文件 &nbsp;-&nbsp; by **wang** [<samp>(6a6fb)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/6a6fb79)
- 添加多级params示例页面文件 &nbsp;-&nbsp; by **wang** [<samp>(5b504)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5b50466)
- 添加 403 404 500一场页面 &nbsp;-&nbsp; by **wang** [<samp>(d67f1)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d67f155)
- 添加登录的最新hook &nbsp;-&nbsp; by **wang** [<samp>(f9b61)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f9b61ff)
- 添加iframe页面文件 &nbsp;-&nbsp; by **wang** [<samp>(3b21b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3b21bd4)
- 添加新增页面的类型文件 &nbsp;-&nbsp; by **wang** [<samp>(609d2)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/609d2a1)
- 添加页面过度css &nbsp;-&nbsp; by **wang** [<samp>(c817b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c817b2e)
- 添加新的页面 &nbsp;-&nbsp; by **wang** [<samp>(5237d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5237d2e)
- 补充事件总线的方式 &nbsp;-&nbsp; by **wang** [<samp>(d3a24)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d3a24a6)
- 把tab相关的聚合在一起 &nbsp;-&nbsp; by **wang** [<samp>(9b2fe)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/9b2fed8)
- 聚合router相关的 &nbsp;-&nbsp; by **wang** [<samp>(abe6a)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/abe6aed)
- 删除首页页面路由 用loader进行跳转 &nbsp;-&nbsp; by **wang** [<samp>(ad4f3)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/ad4f306)
- 首页删除相关的类型 &nbsp;-&nbsp; by **wang** [<samp>(76ad7)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/76ad7b9)
- 优化路由转换函数 &nbsp;-&nbsp; by **wang** [<samp>(3441f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3441fd3)
- 添加initAuth的hook &nbsp;-&nbsp; by **wang** [<samp>(61b3e)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/61b3ed9)
- 优化代码 &nbsp;-&nbsp; by **ohh** [<samp>(32485)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/32485f1)
- 集成auth &nbsp;-&nbsp; by **ohh** [<samp>(5d88a)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5d88af1)
- 优化登录的hook &nbsp;-&nbsp; by **ohh** [<samp>(beed6)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/beed69e)
- 添加首页路由用于转发页面 &nbsp;-&nbsp; by **ohh** [<samp>(84da3)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/84da36a)
- 添加部分tab &nbsp;-&nbsp; by **ohh** [<samp>(5b3bb)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5b3bbc9)
- 完成tab的开发 &nbsp;-&nbsp; by **Ohh-889** [<samp>(25b08)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/25b0850)
- 集成面包屑 &nbsp;-&nbsp; by **Ohh-889** [<samp>(70592)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7059211)
- 完成页面的基本布局 &nbsp;-&nbsp; by **Ohh-889** [<samp>(6d238)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/6d238a2)
- 添加分包 &nbsp;-&nbsp; by **Ohh-889** [<samp>(ffb9d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/ffb9dc6)
- useRoute新增 error的显示 &nbsp;-&nbsp; by **Ohh-889** [<samp>(6ca3a)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/6ca3a0d)
- 完成动态路由的添加 &nbsp;-&nbsp; by **Ohh-889** [<samp>(72de5)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/72de51f)
- 添加多级动态路由的显示 &nbsp;-&nbsp; by **Ohh-889** [<samp>(0a91d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/0a91d7f)
- 添加菜单进行排序 &nbsp;-&nbsp; by **Ohh-889** [<samp>(befbb)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/befbb35)
- 添加多级菜单的路由元信息 &nbsp;-&nbsp; by **Ohh-889** [<samp>(5a459)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5a45969)
- 添加事件总线 &nbsp;-&nbsp; by **Ohh-889** [<samp>(826db)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/826db06)
- 添加关于页面 &nbsp;-&nbsp; by **Ohh-889** [<samp>(d727e)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d727eaf)
- useRouter支持加入参数query &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(4e570)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/4e570ce)
- 支持修改tab的label &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(158dc)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/158dc7c)
- 添加隐藏子菜单路由 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(aa550)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/aa550f2)
- 添加多级菜单 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(7a1f5)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7a1f575)
- 添加给系统功能添加转发界面 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(44ce5)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/44ce59d)
- 添加超级管理员权限页面 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(d2df8)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d2df8c0)
- 添加请求页面 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(a4c76)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a4c7693)
- 添加标签页页面 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(f33ee)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f33eecd)
- 添加切换权限页面 &nbsp;-&nbsp; by **Ohh-889** [<samp>(79e9a)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/79e9aa7)
- 添加初始化缓存路由 &nbsp;-&nbsp; by **Ohh-889** [<samp>(9f358)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/9f3585f)
- 补充路由元信息 &nbsp;-&nbsp; by **Ohh-889** [<samp>(11cc3)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/11cc3f6)
- 添加重置权限 &nbsp;-&nbsp; by **Ohh-889** [<samp>(3376c)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3376cb8)
- 添加角色管理页面 &nbsp;-&nbsp; by **Ohh-889** [<samp>(80798)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/8079893)
- 添加用户详情页面 &nbsp;-&nbsp; by **Ohh-889** [<samp>(32244)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/322444c)
- 完成头像组件的编写 &nbsp;-&nbsp; by **Ohh-889** [<samp>(1a8bf)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/1a8bfe7)
- 添加权限校验 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(9c472)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/9c4725e)
- loading页面也适配暗色模式 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(aaca6)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/aaca6a3)
- 支持修改tab &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(34ff8)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/34ff8bc)
- 优化use-table &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(f6770)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f677023)
- 添加完整的table相关的组件和hook &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(5428d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5428d51)
- 完成用户管理的开发 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(52905)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5290535)
- 完成角色管理的编写 &nbsp;-&nbsp; by **Ohh-889** [<samp>(fad4f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/fad4f03)
- 完成动态路由的编写 &nbsp;-&nbsp; by **Ohh-889** [<samp>(145bf)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/145bfba)
- 添加角色管理详情页面 &nbsp;-&nbsp; by **Ohh-889** [<samp>(02ea5)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/02ea5e6)
- 完成 * 的params 以数组形式返回 &nbsp;-&nbsp; by **Ohh-889** [<samp>(33317)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/33317f2)
- 补充角色管理的展示示例 &nbsp;-&nbsp; by **Ohh-889** [<samp>(75b4b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/75b4b30)
- 完成用户管理详情的示例 &nbsp;-&nbsp; by **Ohh-889** [<samp>(e07cf)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/e07cf2d)
- 添加lodash 依赖 &nbsp;-&nbsp; by **Ohh-889** [<samp>(c8152)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c815205)
- 添加代码提交校验 &nbsp;-&nbsp; by **Ohh-889** [<samp>(7da0d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7da0d42)
- 添加登出路由 &nbsp;-&nbsp; by **Ohh-889** [<samp>(1debe)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/1debef4)
- auth仓库不在做发出网络请求 只存储信息 用hooks的方式完成登录逻辑 &nbsp;-&nbsp; by **Ohh-889** [<samp>(78219)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/782198b)
- 添加路由过渡动画 &nbsp;-&nbsp; by **Ohh-889** [<samp>(74663)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/74663c5)
- 对接动态路由 &nbsp;-&nbsp; by **Ohh-889** [<samp>(fa87c)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/fa87c12)
- 添加代码检查校验 &nbsp;-&nbsp; by **Ohh-889** [<samp>(d2396)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d23963c)
- 更新描述的版本 &nbsp;-&nbsp; by **Ohh-889** [<samp>(44b27)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/44b27b2)
- **hooks**:
  - 去掉相关的form hook &nbsp;-&nbsp; by **wang** [<samp>(b6f78)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b6f78a3)
- **projects**:
  - 添加新的优雅路由文件 &nbsp;-&nbsp; by **wang** [<samp>(57177)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/57177a9)
  - 集成主题切换 &nbsp;-&nbsp; by **wang** [<samp>(3c0de)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3c0dedc)
  - 添加 重置密码的页面 &nbsp;-&nbsp; by **wang** [<samp>(225e3)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/225e30d)
  - 添加 注册的页面 &nbsp;-&nbsp; by **wang** [<samp>(b730a)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b730a21)
  - 添加验证码登陆界面 &nbsp;-&nbsp; by **wang** [<samp>(d0e43)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d0e4329)
  - 添加登录界面 &nbsp;-&nbsp; by **wang** [<samp>(53c73)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/53c73ed)
  - 添加首页路由 &nbsp;-&nbsp; by **wang** [<samp>(2cb59)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/2cb596b)
  - 集成router的相关方法 &nbsp;-&nbsp; by **wang** [<samp>(b6a8f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b6a8f85)
  - 添加blank布局 &nbsp;-&nbsp; by **wang** [<samp>(0f57a)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/0f57a0f)
  - 添加home路由 &nbsp;-&nbsp; by **wang** [<samp>(f6450)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f645090)
  - 集成语言切换 &nbsp;-&nbsp; by **wang** [<samp>(e4b57)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/e4b57d7)
  - 添加buttonIcon组件 &nbsp;-&nbsp; by **wang** [<samp>(7d658)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7d6585c)
  - 添加 系统图标 &nbsp;-&nbsp; by **wang** [<samp>(2c2b9)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/2c2b93b)
  - 添加全局布局组件 &nbsp;-&nbsp; by **wang** [<samp>(7d8e4)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7d8e4b0)
  - 添加全局loading组件 &nbsp;-&nbsp; by **wang** [<samp>(acac1)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/acac156)
  - 添加全局 网络错误页面 &nbsp;-&nbsp; by **wang** [<samp>(a2dda)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a2dda67)
  - 添加全局 无权限 页面 &nbsp;-&nbsp; by **wang** [<samp>(d07b1)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d07b183)
  - 添加404页面 &nbsp;-&nbsp; by **wang** [<samp>(7be1c)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7be1cbf)
  - 添加全局背景组件 &nbsp;-&nbsp; by **wang** [<samp>(5371d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5371d27)
  - 添加全局错误组件 &nbsp;-&nbsp; by **wang** [<samp>(b9ab9)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b9ab989)
  - 集成form的相关hook &nbsp;-&nbsp; by **wang** [<samp>(f1dc0)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f1dc005)
  - 集成antd的相关配置 &nbsp;-&nbsp; by **wang** [<samp>(8c692)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/8c692be)
  - 添加global-content组件 &nbsp;-&nbsp; by **wang** [<samp>(e96ab)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/e96ab16)
  - 集成motion &nbsp;-&nbsp; by **wang** [<samp>(7e6df)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7e6df0b)
- **types**:
  - 添加自动导入的类型 &nbsp;-&nbsp; by **wang** [<samp>(1e369)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/1e369be)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- 修复升级react造成的 svgIcon hook的类型报错 &nbsp;-&nbsp; by **wang** [<samp>(fd890)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/fd89010)
- 修复菜单栏不随着语言切换而切换 &nbsp;-&nbsp; by **Ohh-889** [<samp>(8006e)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/8006e3c)
- 修复有横向菜单时点击子菜单会消失 &nbsp;-&nbsp; by **Ohh-889** [<samp>(3dc6c)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3dc6c59)
- 修复vite配置文件报错 &nbsp;-&nbsp; by **Ohh-889** [<samp>(fea90)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/fea90b9)
- 修复隐藏菜单没有默认跳转和跳转了没有激活菜单 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(9fe31)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/9fe31ba)
- 修复非超级管理员 静态路由下添加错误 &nbsp;-&nbsp; by **Ohh-889** [<samp>(85b71)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/85b718f)
- 修复在切换权限页面 能跳转页面 &nbsp;-&nbsp; by **Ohh-889** [<samp>(f9ecb)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f9ecb63)
- 修复多次点击会调用多次登录接口 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(8feff)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/8feff24)
- 修复会重复添加路由 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(461b6)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/461b648)
- 修复没有登录的时候会修补路由 &nbsp;-&nbsp; by **Ohh-889** [<samp>(8b34e)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/8b34e4d)
- 解决ts报错 &nbsp;-&nbsp; by **Ohh-889** [<samp>(c5e59)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c5e59ab)
- 修复路由切换时因为url为空所以iframe报错 &nbsp;-&nbsp; by **Ohh-889** [<samp>(f5aa8)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f5aa865)
- 修复动态路由 不能正确加载 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(c2580)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c258001)
- **projects**: after logging can still return to the login page .close #24 &nbsp;-&nbsp; by **wang** in https://github.com/soybeanjs/soybean-admin-react/issues/24 [<samp>(399ed)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/399ed5c)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- 主题相关的导入 更加精细 &nbsp;-&nbsp; by **wang** [<samp>(1c6ed)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/1c6ed32)
- 优化代码 &nbsp;-&nbsp; by **wang** [<samp>(da239)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/da239a4)
- 优化代码 &nbsp;-&nbsp; by **wang** [<samp>(7b113)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7b11370)
- 优化代码 &nbsp;-&nbsp; by **wang** [<samp>(0c051)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/0c051c3)
- 优化关于menuUtil的代码 &nbsp;-&nbsp; by **wang** [<samp>(97454)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/97454a9)
- 优化主题设置和tabs缓存的相关代码 &nbsp;-&nbsp; by **wang** [<samp>(fab0c)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/fab0cc7)
- 优化代码 &nbsp;-&nbsp; by **wang** [<samp>(90037)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/9003762)
- 优化相关代码 &nbsp;-&nbsp; by **wang** [<samp>(d98db)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d98db55)
- 优化globalCOntent &nbsp;-&nbsp; by **wang** [<samp>(cbbc5)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/cbbc566)
- 修改menu的实现方式 &nbsp;-&nbsp; by **ohh** [<samp>(24e20)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/24e2081)
- 等待il18的初始化完成 &nbsp;-&nbsp; by **ohh** [<samp>(017c0)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/017c045)
- 优化路由的实现方式 &nbsp;-&nbsp; by **ohh** [<samp>(d9688)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d968843)
- 优化代码 &nbsp;-&nbsp; by **ohh** [<samp>(ce822)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/ce82267)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(8b775)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/8b77543)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(7437e)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7437ecf)
- 修复antd废弃的card的属性 &nbsp;-&nbsp; by **Ohh-889** [<samp>(1d940)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/1d940a7)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(c6377)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c637748)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(47e4a)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/47e4a14)
- 优化tab hook &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(0d82e)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/0d82e61)
- 优化代码 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(a2980)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a29807a)
- 补充useRoute &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(d56da)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d56dad8)
- 优化代码 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(bbbd5)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/bbbd51f)
- 优化代码 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(a333c)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a333cb2)
- 聚合table &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(a92e6)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a92e6c7)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(5d094)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5d094c4)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(9a2f3)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/9a2f35a)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(f675f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f675f19)
- 优化路由守卫只有在路由发生变化时才会重新执行 &nbsp;-&nbsp; by **Ohh-889** [<samp>(87847)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/87847f3)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(424e8)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/424e8a5)
- 优化代码 &nbsp;-&nbsp; by **Ohh-889** [<samp>(67bff)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/67bff43)
- **projects**: optimize code &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(51f31)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/51f3199)

### &nbsp;&nbsp;&nbsp;💅 Refactors

- 删掉props必须为可读的eslint规则 &nbsp;-&nbsp; by **wang** [<samp>(c2860)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c286008)
- 优化入口文件 将多余的代码提到各自的文件中 &nbsp;-&nbsp; by **wang** [<samp>(359f3)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/359f3b2)
- 去掉memo &nbsp;-&nbsp; by **wang** [<samp>(2c27f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/2c27f85)
- 优化代码 &nbsp;-&nbsp; by **wang** [<samp>(6494b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/6494bb0)
- 优化路由相关的类型 &nbsp;-&nbsp; by **wang** [<samp>(39eec)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/39eecf2)
- 为登录页的标题增加动画 &nbsp;-&nbsp; by **wang** [<samp>(037f7)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/037f7ad)
- 删除tab仓库和route仓库 准备重新编写 &nbsp;-&nbsp; by **wang** [<samp>(a6a30)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a6a30fd)
- 删除无用的文件 &nbsp;-&nbsp; by **wang** [<samp>(7938b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7938bbb)
- 更改路由类型 &nbsp;-&nbsp; by **wang** [<samp>(b3e38)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b3e382c)
- 修改面包屑以适应新的路由插件 &nbsp;-&nbsp; by **Ohh-889** [<samp>(f8860)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/f8860cd)
- 给i18key加上路由组的前缀 &nbsp;-&nbsp; by **Ohh-889** [<samp>(5b154)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5b15418)
- 删掉simple-router &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(ab9d9)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/ab9d942)
- 只留loading页面 把插件loading去掉 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(d859e)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d859e23)
- 重构路由handle 写在routes里面 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(6873b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/6873b4a)
- 重新实现过滤初始路由 &nbsp;-&nbsp; by **Ohh-889** [<samp>(316f7)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/316f7ee)
- 将路由元信息搞到生成的路由副文件里面 &nbsp;-&nbsp; by **Ohh-889** [<samp>(3324c)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3324c51)
- 删掉菜单管理 &nbsp;-&nbsp; by **Ohh-889** [<samp>(59ed3)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/59ed3cf)
- 去掉initrouter &nbsp;-&nbsp; by **Ohh-889** [<samp>(c3ac9)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/c3ac982)
- 更新routerHook为initRouter &nbsp;-&nbsp; by **Ohh-889** [<samp>(8dcd4)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/8dcd439)
- 不做登出的鉴权 &nbsp;-&nbsp; by **Ohh-889** [<samp>(a8824)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a882490)
- **packages**:
  - 使用classNames的  替换成clxs &nbsp;-&nbsp; by **wang** [<samp>(be83e)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/be83e80)
- **projects**:
  - refactor theme change &nbsp;-&nbsp; by **wang** [<samp>(43a09)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/43a0999)
  - refactor themeSettings &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(b21c4)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b21c48f)
  - 删除路由和所有页面 开始升级 &nbsp;-&nbsp; by **wang** [<samp>(3d83b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3d83b56)
  - 删除多余的文件 &nbsp;-&nbsp; by **wang** [<samp>(a8be8)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/a8be849)
  - 从app store 中删除掉 关于语言切换 &nbsp;-&nbsp; by **wang** [<samp>(7d7de)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/7d7de22)

### &nbsp;&nbsp;&nbsp;📖 Documentation

- **projects**: update README &nbsp;-&nbsp; by @Azir-11 [<samp>(e4c48)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/e4c48f0)

### &nbsp;&nbsp;&nbsp;📦 Build

- **projects**: 适应新的路由生成插件 &nbsp;-&nbsp; by **wang** [<samp>(59a60)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/59a60ec)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- 升级路由插件 &nbsp;-&nbsp; by **wang** [<samp>(211f1)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/211f1a1)
- 更新依赖 &nbsp;-&nbsp; by **Ohh-889** [<samp>(2a64f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/2a64f65)
- 升级vite 路由插件 &nbsp;-&nbsp; by **Ohh-889** [<samp>(e951b)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/e951b8e)
- 新增新的路由快速刷新忽略 elsint规则 &nbsp;-&nbsp; by **Ohh-889** [<samp>(e8aec)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/e8aec62)
- 为适应新的插件路由级页面组件做出删减 &nbsp;-&nbsp; by **Ohh-889** [<samp>(769ee)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/769ee3e)
- 升级路由插件版本到0.6.1 &nbsp;-&nbsp; by **Ohh-889** [<samp>(d33c9)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/d33c961)
- 修改github主页和预览地址 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(b5cac)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b5cacbd)
- 添加dnd-kit并且删掉react-beautiful-dnd &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(5f622)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/5f62215)
- 把主js文件也打到js目录下面 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(1fc86)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/1fc86c7)
- 更新README &nbsp;-&nbsp; by **Ohh-889** [<samp>(1b42f)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/1b42f01)
- 更新依赖 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(3f1c6)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/3f1c6ec)
- **deps**:
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(b40aa)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b40aaad)
  - 升级依赖 &nbsp;-&nbsp; by **wang** [<samp>(05ef9)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/05ef9be)
  - 集成motion &nbsp;-&nbsp; by **wang** [<samp>(37ae5)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/37ae52c)
  - 删掉classNames 用clsx代替 &nbsp;-&nbsp; by **wang** [<samp>(b57b4)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/b57b413)
- **projects**:
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(0edc4)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/0edc4fa)
  - temporarily turn off code verification &nbsp;-&nbsp; by **wang** [<samp>(ed542)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/ed54217)

### &nbsp;&nbsp;&nbsp;✅ Tests

- 测试常量路由的实现方式 &nbsp;-&nbsp; by **wang** [<samp>(49c3d)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/49c3d48)

### &nbsp;&nbsp;&nbsp;🤖 CI

- **build.proxy**: 显示实际请求的 url 地址 &nbsp;-&nbsp; by **心灵骇客** [<samp>(64f12)</samp>](https://github.com/soybeanjs/soybean-admin-react/commit/64f12a3)

### &nbsp;&nbsp;&nbsp;❤️ Contributors

[![Azir-11](https://github.com/Azir-11.png?size=48)](https://github.com/Azir-11)&nbsp;&nbsp;
[COBORGA-8FFVDVM\cob](mailto:<EMAIL>),&nbsp;[心灵骇客](mailto:<EMAIL>)

## [v1.2.0](https://github.com/mufeng889/react-soybean-admin/compare/v1.1.1...v1.2.0) (2024-12-15)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **packages**:
  - use-array add findItem & reset &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(64e0b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/64e0bb3)
  - add event bus hook &nbsp;-&nbsp; by **wang** [<samp>(9f145)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/9f14555)
- **projects**:
  - supplement eslint rules &nbsp;-&nbsp; by **wang** [<samp>(1a631)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1a6314e)
  - add event bus hooks demo &nbsp;-&nbsp; by **wang** [<samp>(b7080)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b7080ed)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- If the url is /login/register, the system is redirected to /login/pwd-login by default  .close #22 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** in https://github.com/mufeng889/react-soybean-admin/issues/22 [<samp>(9ddae)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/9ddae85)
- **projects**: fix Reverse the top blending mode. Select Secondary tab. Primary tab is not displayed correctly .close #21 &nbsp;-&nbsp; by **wang** in https://github.com/mufeng889/react-soybean-admin/issues/21 [<samp>(b2f07)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b2f071c)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- **projects**:
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(72b38)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/72b3898)
  - optimize code &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** [<samp>(b9f0e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b9f0ecf)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **other**: update vscode settings &nbsp;-&nbsp; by **wang** [<samp>(70d5f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/70d5fbb)
- **projects**: update readme &nbsp;-&nbsp; by **wang** [<samp>(82978)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/829780d)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v1.1.1](https://github.com/mufeng889/react-soybean-admin/compare/v1.1.0...v1.1.1) (2024-11-24)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **projects**: fix when clicking Close tab TAB occasionally white screen &nbsp;-&nbsp; by **wang** [<samp>(ae2c3)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ae2c385)

### &nbsp;&nbsp;&nbsp;🎨 Styles

- **projects**: add eslint rules &nbsp;-&nbsp; by **wang** [<samp>(d27ac)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d27ac84)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v1.1.0](https://github.com/mufeng889/react-soybean-admin/compare/v1.0.0...v1.1.0) (2024-11-09)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **hooks**:
  - add useMobile hook &nbsp;-&nbsp; by **wang** [<samp>(2b2c7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2b2c763)
- **projects**:
  - add tag-map contant &nbsp;-&nbsp; by **wang** [<samp>(605ee)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/605ee30)
  - add route config example &nbsp;-&nbsp; by **wang** [<samp>(1587b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1587bcd)
  - add role-manage &nbsp;-&nbsp; by **wang** [<samp>(c315f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c315f10)
  - add new tag-map & auto-import antd components type &nbsp;-&nbsp; by **wang** [<samp>(57fb4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/57fb4fb)
  - add menu-manage &nbsp;-&nbsp; by **wang** [<samp>(b10f5)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b10f577)
  - add user-modal & role-modal form rules &nbsp;-&nbsp; by **wang** [<samp>(646a5)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/646a5f0)
- **types**:
  - add page types &nbsp;-&nbsp; by **wang** [<samp>(804f3)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/804f34b)
  - add new auto import antd components &nbsp;-&nbsp; by **wang** [<samp>(45bbc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/45bbc96)
  - add new auto-import antd components &nbsp;-&nbsp; by **wang** [<samp>(df1e2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/df1e272)
  - add page operateDrawerProps &nbsp;-&nbsp; by **wang** [<samp>(3b822)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3b822d3)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **hooks**:
  - after the user manages the edit modification submission, when he clicks Add, the data just edited is displayed  .close #18 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** in https://github.com/mufeng889/react-soybean-admin/issues/18 [<samp>(c4f1e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c4f1eb3)
- **projects**:
  - home: fix the button have  same key &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d583c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d583cce)
  - optimize theme-drawer code to remove waring &nbsp;-&nbsp; by **wang** [<samp>(d489c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d489c9c)
  - fix table scroll don't auto scroll &nbsp;-&nbsp; by **wang** [<samp>(2277d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2277d19)
  - login after don't go login page .close #14 &nbsp;-&nbsp; by **COBORGA-8FFVDVM\cob** in https://github.com/mufeng889/react-soybean-admin/issues/14 [<samp>(a4b51)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a4b514c)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- **projects**:
  - optimize use-manager code &nbsp;-&nbsp; by **wang** [<samp>(effac)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/effacf9)
  - optimize userOperateDrawer code &nbsp;-&nbsp; by **wang** [<samp>(46bc4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/46bc404)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(14e87)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/14e8768)
  - optimize about page code &nbsp;-&nbsp; by **wang** [<samp>(bb862)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/bb86296)

### &nbsp;&nbsp;&nbsp;💅 Refactors

- **hooks**:
  - usetable return data add searchProps &nbsp;-&nbsp; by **wang** [<samp>(6c0d7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6c0d7ce)
- **projects**:
  - simplifies and extends useTableOperate &nbsp;-&nbsp; by **wang** [<samp>(283fd)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/283fdde)
  - use-Table handleEdit support income data &nbsp;-&nbsp; by **wang** [<samp>(56b0d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/56b0d05)
  - update keep-alive usage &nbsp;-&nbsp; by **wang** [<samp>(dcebd)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/dcebd86)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**:
  - delete immer &nbsp;-&nbsp; by **wang** [<samp>(8ace3)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8ace3fc)
- **projects**:
  - update CHANGELOG.zh-CN &nbsp;-&nbsp; by **wang** [<samp>(6d986)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6d98622)
  - update README &nbsp;-&nbsp; by **wang** [<samp>(a3115)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a31159d)
  - update deps & sass usage &nbsp;-&nbsp; by **wang** [<samp>(7da52)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7da5263)
  - optimize code & update vscode settings &nbsp;-&nbsp; by **wang** [<samp>(2ecb0)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2ecb099)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v1.0.0](https://github.com/mufeng889/react-soybean-admin/compare/v0.3.2...v1.0.0) (2024-10-06)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **packages**:
  - @sa/hooks add useRequest &nbsp;-&nbsp; by **wang** [<samp>(e5cdc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e5cdcc9)
  - hooks: add use-array & example &nbsp;-&nbsp; by **wang** [<samp>(02483)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/02483b5)
- **projects**:
  - add menu functions &nbsp;-&nbsp; by **wang** [<samp>(0b14d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0b14deb)
  - support add parent when add route &nbsp;-&nbsp; by **wang** [<samp>(084bf)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/084bf89)
  - support dynamic add route & optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6f3ad)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6f3adca)
  - add before guard &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(13b0c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/13b0cab)
  - @sa/axios: add response to flatRequest when success &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(92e3c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/92e3cec)
  - does the configuration support automatic updates. &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fb758)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fb7583a)
  - add details page to show loader for data router &nbsp;-&nbsp; by **wang** [<samp>(7928b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7928bd6)
  - the topic configuration replication function was added &nbsp;-&nbsp; by **wang** [<samp>(e3d7a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e3d7a99)
  - add animation &nbsp;-&nbsp; by **wang** [<samp>(ea5d7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ea5d7c6)
  - add useMeta &nbsp;-&nbsp; by **wang** [<samp>(d0c6a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d0c6a37)
  - add keep-alive &nbsp;-&nbsp; by **wang** [<samp>(ed7e7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ed7e793)
  - login supports accessible operation &nbsp;-&nbsp; by **wang** [<samp>(d2dae)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d2dae2d)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - eix tab  can't click on  mobile side &nbsp;-&nbsp; by **wang** [<samp>(e0141)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e01410a)
  - support pass state and fix judgments before jumpe &nbsp;-&nbsp; by **wang** [<samp>(34935)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3493583)
  - fix useRouter type &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(32628)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/32628df)
  - failure to return in some fast new cases results in no initialization . close #8 &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** in https://github.com/mufeng889/react-soybean-admin/issues/8 [<samp>(cfe46)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cfe46ea)
- **projects**:
  - fix top menu abnormal &nbsp;-&nbsp; by **wang** [<samp>(5e1f7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5e1f789)
  - fixed abnormal display of dynamic switching size menu &nbsp;-&nbsp; by **wang** [<samp>(79c1a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/79c1ae1)
  - fix eslint errors &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fec80)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fec80a1)
  - click tab left menu openkeys does not change &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(f3f57)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f3f570b)
  - fix route type & remove startTransition &nbsp;-&nbsp; by **wang** [<samp>(fac36)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fac368b)
  - Fixed redirection when switching roles & init tab no cache &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(58d1f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/58d1feb)
  - fix refresh token when meet multi requests &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fbe7d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fbe7ddb)
  - in big screen has scroll bar &nbsp;-&nbsp; by **wang** [<samp>(cb942)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cb94245)
  - fix after  menu expansion submenu not open &nbsp;-&nbsp; by **wang** [<samp>(c96c9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c96c964)
  - fix shrink mess in mixed mode &nbsp;-&nbsp; by **wang** [<samp>(0c6fb)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0c6fba6)
  - fix global-tab click conflict with contextmenu &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a32f5)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a32f507)
  - Fixed submenu opening when shrinking &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(77f2b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/77f2b6a)
  - fixed the sidebar automatically expanding when switching between mobile and pc &nbsp;-&nbsp; by **wang** [<samp>(0abdd)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0abdd0c)
  - failed to switch sidebar language &nbsp;-&nbsp; by **wang** [<samp>(75307)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/753079f)
  - the reproduction environment can cache the theme configuration &nbsp;-&nbsp; by **wang** [<samp>(50932)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/50932b7)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- **packages**:
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(5f78e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5f78e52)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(26f05)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/26f0579)
  - simple-router: optimize code &nbsp;-&nbsp; by **wang** [<samp>(27d5f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/27d5f7e)
- **projects**:
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(85b64)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/85b6483)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(21d28)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/21d28b0)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(b29bc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b29bceb)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(f6fd4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f6fd4f8)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(43f8b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/43f8b45)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(a5cc9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a5cc93a)

### &nbsp;&nbsp;&nbsp;💅 Refactors

- **packages**:
  - update Route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(8795b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8795b2f)
  - @sa/hooks: use-request fir axios &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(3dbe7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3dbe701)
  - @sa/simple-router: stable useRoute &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6cf09)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6cf09f9)
  - @sa/materials: remove tab close shortcut by mouse &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(edb3e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/edb3e69)
- **projects**:
  - add logout route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(df689)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/df689df)
  - refactor simple-router &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d7861)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d78613c)
  - combine theme tokens and theme settings &nbsp;-&nbsp; by **wang** [<samp>(8d703)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8d703d9)
  - remove dark sidebar configuration &nbsp;-&nbsp; by **wang** [<samp>(f9582)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f958280)
  - Modify card's global style search table to use Collapse &nbsp;-&nbsp; by **wang** [<samp>(17c2e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/17c2ed9)
  - change antd's colorBgContainer &nbsp;-&nbsp; by **wang** [<samp>(0b65c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0b65cea)
  - change sidebar animation &nbsp;-&nbsp; by **wang** [<samp>(f9297)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f92972e)

### &nbsp;&nbsp;&nbsp;📖 Documentation

- **projects**: update CHANGELOG &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a13a7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a13a70d)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**:
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(1dad4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1dad4f0)
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6ff15)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6ff150b)
  - remove transition-group-plus &nbsp;-&nbsp; by **wang** [<samp>(36996)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3699680)
  - add framer-motion & remove react-transition-group &nbsp;-&nbsp; by **wang** [<samp>(abada)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/abada7f)
- **projects**:
  - update CHANGELOG &nbsp;-&nbsp; by **wang** [<samp>(264f9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/264f9c6)
  - update scss config &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(480c8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/480c869)
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(0d855)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0d855e7)
- **types**:
  - remove type declaration for document.startViewTransition (TypeScript 5.6 inclnudes it) &nbsp;-&nbsp; by **wang** [<samp>(cef47)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cef474d)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v1.0.0-beta.1](https://github.com/mufeng889/react-soybean-admin/compare/v0.3.2...v1.0.0-beta.1) (2024-09-14)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **packages**:
  - @sa/hooks add useRequest &nbsp;-&nbsp; by **wang** [<samp>(e5cdc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e5cdcc9)
- **projects**:
  - add menu functions &nbsp;-&nbsp; by **wang** [<samp>(0b14d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0b14deb)
  - support add parent when add route &nbsp;-&nbsp; by **wang** [<samp>(084bf)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/084bf89)
  - support dynamic add route & optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6f3ad)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6f3adca)
  - add before guard &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(13b0c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/13b0cab)
  - @sa/axios: add response to flatRequest when success &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(92e3c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/92e3cec)
  - does the configuration support automatic updates. &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fb758)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fb7583a)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - eix tab  can't click on  mobile side &nbsp;-&nbsp; by **wang** [<samp>(e0141)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e01410a)
  - support pass state and fix judgments before jumpe &nbsp;-&nbsp; by **wang** [<samp>(34935)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3493583)
  - fix useRouter type &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(32628)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/32628df)
  - failure to return in some fast new cases results in no initialization . close #8 &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** in https://github.com/mufeng889/react-soybean-admin/issues/8 [<samp>(cfe46)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cfe46ea)
- **projects**:
  - fix top menu abnormal &nbsp;-&nbsp; by **wang** [<samp>(5e1f7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5e1f789)
  - fixed abnormal display of dynamic switching size menu &nbsp;-&nbsp; by **wang** [<samp>(79c1a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/79c1ae1)
  - fix eslint errors &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fec80)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fec80a1)
  - click tab left menu openkeys does not change &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(f3f57)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f3f570b)
  - fix route type & remove startTransition &nbsp;-&nbsp; by **wang** [<samp>(fac36)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fac368b)
  - Fixed redirection when switching roles & init tab no cache &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(58d1f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/58d1feb)
  - fix refresh token when meet multi requests &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fbe7d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fbe7ddb)
  - in big screen has scroll bar &nbsp;-&nbsp; by **wang** [<samp>(cb942)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cb94245)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- **packages**:
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(5f78e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5f78e52)
- **projects**:
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(85b64)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/85b6483)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(21d28)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/21d28b0)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(b29bc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b29bceb)

### &nbsp;&nbsp;&nbsp;💅 Refactors

- **packages**:
  - update Route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(8795b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8795b2f)
  - @sa/hooks: use-request fir axios &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(3dbe7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3dbe701)
  - @sa/simple-router: stable useRoute &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6cf09)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6cf09f9)
- **projects**:
  - add logout route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(df689)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/df689df)
  - refactor simple-router &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d7861)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d78613c)
  - combine theme tokens and theme settings &nbsp;-&nbsp; by **wang** [<samp>(8d703)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8d703d9)
  - remove dark sidebar configuration &nbsp;-&nbsp; by **wang** [<samp>(f9582)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f958280)

### &nbsp;&nbsp;&nbsp;📖 Documentation

- **projects**: update CHANGELOG &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a13a7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a13a70d)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**:
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(1dad4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1dad4f0)
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6ff15)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6ff150b)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[DESKTOP-31IBRMI\Administrator](mailto:<EMAIL>)

## [v0.3.2](https://github.com/mufeng889/react-soybean-admin/compare/v0.3.0...v0.3.2) (2024-09-07)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **deps**:
  - add typewriter &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(0651b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0651b64)
- **packages**:
  - add remove route & If the route to be jumped is the current route, no jump occurs &nbsp;-&nbsp; by **wang** [<samp>(a75c2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a75c25e)
  - Synchronize the useRouterPush of soybean &nbsp;-&nbsp; by **wang** [<samp>(04577)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/04577cd)
- **proejects**:
  - routing and layout error boundaries combine &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(7de3c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7de3c59)
- **projects**:
  - change document address &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(7ced8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7ced849)
  - synchronize updates to soybean's axios &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6401f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6401f0b)
  - add common internationalization configuration &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a702d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a702d5c)
  - the routing information file can be created on the command line &nbsp;-&nbsp; by **wang** [<samp>(6e77e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6e77edc)
  - update elegant-route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d4a65)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d4a655f)
  - iframe add skeleton &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e21af)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e21afb8)
  - add components auto-import &nbsp;-&nbsp; by **wang** [<samp>(b60f2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b60f2ea)
  - add antd auto-import & close route log info &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(5879f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5879fd1)
  - add full screen watermarke &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(103b6)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/103b643)
  - optimize code style &  exception page add button click  event &nbsp;-&nbsp; by **wang** [<samp>(f1dcd)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f1dcd68)
  - Synchronize the useRouterPush of soybean &nbsp;-&nbsp; by **wang** [<samp>(308df)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/308dfdd)
  - add version update notifications &nbsp;-&nbsp; by **wang** [<samp>(3f25b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3f25b19)
  - use json5 resolve env VITE_OTHER_SERVICE_BASE_URL & fix proxy enable & updated international language &nbsp;-&nbsp; by **wang** [<samp>(15769)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1576922)
  - add color fading mode &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(10b2a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/10b2a65)
- **prxojects**:
  - change useRouter declaration type &nbsp;-&nbsp; by **wang** [<samp>(f27d4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f27d4c3)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - can jump to params & not-found &nbsp;-&nbsp; by **wang** [<samp>(6241a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6241a81)
- **projects**:
  - fixed tables not showing scrollbars when screen width changes . close #5 &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** in https://github.com/mufeng889/react-soybean-admin/issues/5 [<samp>(bc673)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/bc67301)
  - repair tabcannot be modified &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(0d3ca)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0d3cae0)
  - update redirect &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(dc18c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/dc18c38)
  - fixed  left menu mixed mode shrinks  menu &nbsp;-&nbsp; by **wang** [<samp>(77dac)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/77dacbd)
- **types**:
  - fix the type of TableApiFn &nbsp;-&nbsp; by **wang** [<samp>(b8daf)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b8daf7c)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(00bdc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/00bdccb)
- **packages**:
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(4f6a2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/4f6a2e1)
- **projects**:
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a9854)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a98549a)
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(aa3bc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/aa3bc09)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(b407e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b407ec4)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(6ce4f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6ce4f26)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(8f9a8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8f9a86c)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(0402b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0402b46)

### &nbsp;&nbsp;&nbsp;💅 Refactors

- **packages**:
  - Refactoring useRoute &nbsp;-&nbsp; by **wang** [<samp>(b9b55)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b9b55d3)
- **projects**:
  - change css vars mount to root &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(c59ed)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c59edf6)
  - refactor part of the menu code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d19aa)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d19aa0b)
  - the vercal-mix reconstruction is complete &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(dab53)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/dab5333)
  - refactor the menu section code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(1f1ef)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1f1efbb)
  - Refactoring useMenu &nbsp;-&nbsp; by **wang** [<samp>(185ff)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/185ff72)
  - refactor global menu & support reversed-horizontal-mix-menu &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(132fa)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/132fa6f)

### &nbsp;&nbsp;&nbsp;📦 Build

- **deps**: add deps vite-plugin-checker &nbsp;-&nbsp; by **wang** [<samp>(24454)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2445424)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **README**:
  - update README &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e9ebe)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e9ebeb2)
  - update README &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a084d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a084d21)
- **deps**:
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(f63e7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f63e71e)
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(ce564)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ce564ce)
  - remove lodash-es &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d487c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d487c9b)
  - add simple-git-hooks lint-staged &nbsp;-&nbsp; by **wang** [<samp>(9c977)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/9c977fd)
- **other**:
  - print project information in console &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d9cf8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d9cf87a)
- **projects**:
  - add vscode configuration & add vite preload &nbsp;-&nbsp; by **wang** [<samp>(48cd0)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/48cd07e)

### &nbsp;&nbsp;&nbsp;🎨 Styles

- **proejcts**:
  - optimized code format &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e2c03)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e2c0391)
- **projects**:
  - optimized code style &nbsp;-&nbsp; by **wang** [<samp>(f5fed)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f5fed34)
  - change component classification &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e41a9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e41a925)
  - optimize code style &nbsp;-&nbsp; by **wang** [<samp>(0eded)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0eded8d)
  - optimize code style &nbsp;-&nbsp; by **wang** [<samp>(95243)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/95243a0)
  - optimized code &nbsp;-&nbsp; by **wang** [<samp>(28d68)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/28d68fc)
  - reduce the padding of header buttoon &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(1a343)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1a34371)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v0.3.1](https://github.com/mufeng889/react-soybean-admin/compare/v0.3.0...v0.3.1) (2024-08-31)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **proejects**:
  - routing and layout error boundaries combine &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(7de3c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7de3c59)
- **projects**:
  - change document address &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(7ced8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7ced849)
  - synchronize updates to soybean's axios &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6401f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6401f0b)
  - add common internationalization configuration &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a702d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a702d5c)
  - the routing information file can be created on the command line &nbsp;-&nbsp; by **wang** [<samp>(6e77e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6e77edc)
  - update elegant-route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d4a65)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d4a655f)
  - iframe add skeleton &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e21af)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e21afb8)
  - add components auto-import &nbsp;-&nbsp; by **wang** [<samp>(b60f2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b60f2ea)
  - add antd auto-import & close route log info &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(5879f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5879fd1)
  - add full screen watermarke &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(103b6)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/103b643)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - can jump to params & not-found &nbsp;-&nbsp; by **wang** [<samp>(6241a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6241a81)
- **projects**:
  - fixed tables not showing scrollbars when screen width changes . close #5 &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** in https://github.com/mufeng889/react-soybean-admin/issues/5 [<samp>(bc673)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/bc67301)
  - repair tabcannot be modified &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(0d3ca)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0d3cae0)
  - update redirect &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(dc18c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/dc18c38)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(00bdc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/00bdccb)
- **packages**:
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(4f6a2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/4f6a2e1)
- **projects**:
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a9854)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a98549a)
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(aa3bc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/aa3bc09)

### &nbsp;&nbsp;&nbsp;📦 Build

- **deps**: add deps vite-plugin-checker &nbsp;-&nbsp; by **wang** [<samp>(24454)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2445424)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **README**:
  - update README &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e9ebe)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e9ebeb2)
  - update README &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a084d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a084d21)
- **deps**:
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(f63e7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f63e71e)
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(ce564)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ce564ce)
  - remove lodash-es &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d487c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d487c9b)
- **projects**:
  - add vscode configuration & add vite preload &nbsp;-&nbsp; by **wang** [<samp>(48cd0)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/48cd07e)

### &nbsp;&nbsp;&nbsp;🎨 Styles

- **proejcts**:
  - optimized code format &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e2c03)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e2c0391)
- **projects**:
  - optimized code style &nbsp;-&nbsp; by **wang** [<samp>(f5fed)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f5fed34)
  - change component classification &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e41a9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e41a925)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[DESKTOP-31IBRMI\Administrator](mailto:<EMAIL>)

## [v0.3.0](https://github.com/mufeng889/react-soybean-admin/compare/v0.2.1...v0.3.0) (2024-08-20)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **packages**: perfect routing method &nbsp;-&nbsp; by **wang** [<samp>(7a15b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7a15b1b)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - fix repeat routing &nbsp;-&nbsp; by **wang** [<samp>(98a71)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/98a7117)
- **projects**:
  - prevent local storage conflicts &nbsp;-&nbsp; by @Azir-11 [<samp>(cc2d4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cc2d470)
  - fix Fix type error type error  . close #3 &nbsp;-&nbsp; by **wang** in https://github.com/mufeng889/react-soybean-admin/issues/3 [<samp>(d9237)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d923752)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- optimized code &nbsp;-&nbsp; by **wang** [<samp>(f94b5)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f94b58b)
- **code**:
  - optimize the experience &nbsp;-&nbsp; by **wang** [<samp>(6a135)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6a1354a)
- **packages**:
  - optimize simple-router code &nbsp;-&nbsp; by **wang** [<samp>(c6ea8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c6ea8b0)
  - optimized  simple-router code &nbsp;-&nbsp; by **wang** [<samp>(edfb1)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/edfb1f6)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **package**: improve package content . close #4 &nbsp;-&nbsp; by **wang** in https://github.com/mufeng889/react-soybean-admin/issues/4 [<samp>(a2dde)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a2dde59)

### &nbsp;&nbsp;&nbsp;❤️ Contributors

[![Azir-11](https://github.com/Azir-11.png?size=48)](https://github.com/Azir-11)&nbsp;&nbsp;
[wang](mailto:<EMAIL>),&nbsp;

## [v0.2.1](https://github.com/mufeng889/react-soybean-admin/compare/v0.2.0...v0.2.1) (2024-08-14)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **proejects**: use other methods to replace toSorted for better compatibility with more browsers &nbsp;-&nbsp; by **wang** [<samp>(5bc13)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5bc13d6)
- **projects**: fixed tab not switching colors when switching color themes &nbsp;-&nbsp; by **wang** [<samp>(5afba)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5afba5e)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**: update deps &nbsp;-&nbsp; by **wang** [<samp>(95d38)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/95d38fe)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v0.2.0](https://github.com/mufeng889/react-soybean-admin/compare/undefined...v0.2.0) (2024-08-08)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **.github**:
  - add  workflows &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d3e8c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d3e8c65)
- **.npmr**:
  - cancel Taobao mirror address &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(1f90f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1f90f11)
- **pachages**:
  - add parse query &  stringify query &nbsp;-&nbsp; by **wang** [<samp>(c0134)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c0134c7)
- **packages**:
  - add route Transition animation &nbsp;-&nbsp; by **wang** [<samp>(da94e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/da94e7c)
- **projects**:
  - init project &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(651d3)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/651d3bb)
  - add configuration &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6c976)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6c976ba)
  - remove restrictions on locked files &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(9138d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/9138d77)
  - perfect user interface &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(1cc8a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1cc8a60)
  - add il18 for error boundary component &nbsp;-&nbsp; by **wang** [<samp>(8d6cc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8d6cc79)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- optimized code & remove useless code &nbsp;-&nbsp; by **wang** [<samp>(2ac61)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2ac611f)
- **projects**: place the error boundary under the root route & add the internationalized language of the login page &nbsp;-&nbsp; by **wang** [<samp>(1e4a1)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1e4a109)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**:
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(beea6)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/beea6ad)
  - add deps vite-plugin-remove-console & optimize code style &nbsp;-&nbsp; by **wang** [<samp>(6387f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6387f23)
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(965df)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/965df84)

### &nbsp;&nbsp;&nbsp;🤖 CI

- **vercel**: add vercel profile &nbsp;-&nbsp; by **wang** [<samp>(8b275)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8b27596)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

