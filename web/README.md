<div align="center">
 <img src="./public/favicon.svg" width="160" />
 <h1>React SoybeanAdmin</h1>
  <span><a href="./README.en-US.md">English</a> | 中文</span>
</div>

---

[![license](https://img.shields.io/badge/license-MIT-green.svg)](./LICENSE)

> [!NOTE]
> 如果您觉得 `React SoybeanAdmin` 对您有所帮助，或者您喜欢我们的项目，请在 GitHub 上给我们一个 ⭐️。您的支持是我们持续改进和增加新功能的动力！感谢您的支持！

## 特别鸣谢

本项目是基于 [Soybean](https://github.com/honghuangdc) 开发的优秀开源项目 [Soybean Admin](https://github.com/soybeanjs/soybean-admin) 的 React 版本实现。在此特别感谢 Soybean 的开源贡献,为中后台开发提供了优秀的解决方案。如果您喜欢本项目,也请给原作者的 [Soybean Admin](https://github.com/soybeanjs/soybean-admin) 点个 star ⭐️。

## 简介

[`React SoybeanAdmin`](https://github.com/soybeanjs/soybean-admin-react) 是一个基于 React19 技术栈的清新优雅中后台模版。项目采用了最新的前端技术栈:

- 🚀 React 19 + ReactRouter V7 + Redux/toolkit
- 🎨 Ant Design + UnoCSS
- 📦 Vite 6 + TypeScript
- 🔐 基于角色的权限管理
- 📱 移动端适配
- 🌍 国际化支持
- 🎯 Mock 数据方案
- 📚 详尽的文档支持

项目特点:

- 💡 代码规范严谨,架构清晰优雅
- ⚡️ 开箱即用,无需复杂配置
- 🛠️ 丰富的组件和主题配置
- 📋 自动化的文件路由系统
- 🔧 完善的类型支持
- 📱 响应式设计,完美适配移动端
- 🎨 乐观`UI` 项目出现错误自动捕获错误,并显示友好界面,帮助用户快速定位和解决问题,还可以再组件内监控埋点上报。
- 🚀 丰富的路由功能：基于 React-Router V7 扩展了路由 API，提供类似 Next.js一样的约定式文件路由，也可以自己添加复用路由。
- ⚡️ 命令行工具：内置高效的命令行工具，git提交、删除文件、发布等。

无论是学习最新前端技术,还是开发企业级中后台项目,React SoybeanAdmin 都是您的不二之选。

## 分支

- `master` 分支: 最新稳定版本,基于 React19 + ReactRouter V7  版本
- `v18-router6` 分支: 基于 React18 + ReactRouter V6 版本

有问题都是会进行修复,如果需要使用旧版本,请切换到对应分支。 v18-router6 在未来3-5年依然会是市面上较为先进 功能强大的版本

## 版本

### React版本

- **React19 版本:**
  - [预览地址](https://react.soybeanjs.cn/)
  - [国内加速访问]( https://react-soybean-admin.pages.dev/)
  - [Github 仓库](https://github.com/mufeng889/react-soybean-admin)
  - [Gitee 仓库](https://gitee.com/sjgk_dl/react-admin)

#### 文档

- [地址](https://react-docs.soybeanjs.cn/guide)

### Vue版本

- **NaiveUI 版本:**
  - [预览地址](https://naive.soybeanjs.cn/) - 基于 Vue3 + NaiveUI 构建的清新优雅后台管理模板
  - [Github 仓库](https://github.com/soybeanjs/soybean-admin) - 获取最新源码，参与开源贡献
  - [Gitee 仓库](https://gitee.com/honghuangdc/soybean-admin) - 国内镜像仓库，访问更快捷
  - 特点:
    - 完整的 TypeScript 支持
    - 丰富的主题配置
    - 优雅的代码风格
    - 完善的文档说明
- **AntDesignVue 版本:**
  - [预览地址](https://antd.soybeanjs.cn/)
  - [Github 仓库](https://github.com/soybeanjs/soybean-admin-antd)
  - [Gitee 仓库](https://gitee.com/honghuangdc/soybean-admin-antd)

- **旧版:**
  - [预览地址](https://legacy.soybeanjs.cn/)
  - [Github 仓库](https://github.com/soybeanjs/soybean-admin/tree/legacy)


## 符合Ant Design风格的保姆级文档

- [地址](https://react-soybean-docs.ohh-889.com/index-cn?theme=dark)
![](https://ohh-**********.cos.ap-nanjing.myqcloud.com/docs-home.jpg)

## 示例图片

![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-01.png)
![](https://ohh-**********.cos.ap-nanjing.myqcloud.com/mobile.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-02.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-03.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-04.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-05.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-06.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-07.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-08.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-09.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-10.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-mobile.png)

## 使用

**环境准备**

确保你的环境满足以下要求：

- **git**: 你需要git来克隆和管理项目版本。
- **NodeJS**: >=18.12.0，推荐 18.19.0 或更高。
- **pnpm**: >= 8.7.0，推荐 8.14.0 或更高。

**克隆项目**

```bash
git clone https://github.com/soybeanjs/soybean-admin.git
```

**安装依赖**

```bash
pnpm i
```

> 由于本项目采用了 pnpm monorepo 的管理方式，因此请不要使用 npm 或 yarn 来安装依赖。

**启动项目**

```bash
pnpm dev
```

**构建项目**

```bash
pnpm build
```

## 如何贡献

我们热烈欢迎并感谢所有形式的贡献。如果您有任何想法或建议，欢迎通过提交 [pull requests](https://github.com/soybeanjs/soybean-admin-react/pulls) 或创建 GitHub [issue](https://github.com/soybeanjs/soybean-admin-react/issues) 来分享。

## 团队理念

- 欢迎各位小伙伴一起交流、讨论，彼此学习、共同进步。
- 项目采用 **MIT** 开源协议，永久免费使用，无需担忧版权问题。
- 任何关于功能扩展、Bug 修复、或文档纠正的贡献都十分欢迎，也鼓励你提交 **PR**，哪怕只是修正一个错别字。

## Git 提交规范

本项目已内置 `commit` 命令，您可以通过执行 `pnpm commit` 来生成符合 [Conventional Commits]([conventionalcommits](https://www.conventionalcommits.org/)) 规范的提交信息。在提交PR时，请务必使用 `commit` 命令来创建提交信息，以确保信息的规范性。

## 浏览器支持

推荐使用最新版的 Chrome 浏览器进行开发，以获得更好的体验。

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/archive/internet-explorer_9-11/internet-explorer_9-11_48x48.png" alt="IE" width="24px" height="24px"  />](http://godban.github.io/browsers-support-badges/) | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/) | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/) | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/) | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/) |
| --- | --- | --- | --- | --- |
| not support | last 2 versions | last 2 versions | last 2 versions | last 2 versions |

## 开源作者

[Ohh-889](https://github.com/mufeng889)

[Soybean](https://github.com/honghuangdc)

## 贡献者

感谢以下贡献者的贡献。如果您想为本项目做出贡献，请参考 [如何贡献](#如何贡献)。

<a href="https://github.com/mufeng889/react-soybean-admin/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=mufeng889/react-soybean-admin" />
</a>

## 交流

`React Soybean` 是完全开源免费的项目，在帮助开发者更方便地进行中大型管理系统开发，同时也提供微信和 QQ 交流群，使用问题欢迎在群内提问。

  <div>
   <p>QQ交流群</p>
    <img src="https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/qq-soybean-admin-4.jpg" style="width:200px" />
  </div>

 <div>
  <p>添加下面微信邀请进微信群</p>
  <img src="https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/wechat-soybeanjs.jpg" style="width:200px" />
 </div>

 <div>
  <p>添加下面微信邀请进微信群</p>
  <img src="
  https://ohh-**********.cos.ap-nanjing.myqcloud.com/ohh-889.jpg" style="width:200px" />
 </div>

## 开源协议

项目基于 [MIT © 2021 Soybean](./LICENSE) 协议，仅供学习参考，商业使用请保留作者版权信息，作者不保证也不承担任何软件的使用风险。

## 祝福与展望

非常感谢你选择 **soybean-admin-react**，愿它能在你的工作和学习中带来便利与收获。祝所有使用者在工作和生活中都能顺利进步、健康平安。欢迎大家积极参与、贡献代码，共同将 **soybean-admin-react** 打造得更加完善与强大！