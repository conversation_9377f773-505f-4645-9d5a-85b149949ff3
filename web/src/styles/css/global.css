@import './reset.css';
@import './nprogress.css';

html,
body,
#root {
  height: 100%;
}

html {
  overflow-x: hidden;
}

* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.25) transparent;
}

.ant-table-header::-webkit-scrollbar {
  background-color: transparent;
}

html.grayscale {
  filter: grayscale(100%);
}

::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}
::view-transition-old(root) {
  z-index: 9999;
}
::view-transition-new(root) {
  z-index: 1;
}
.dark::view-transition-old(root) {
  z-index: 1;
}
.dark::view-transition-new(root) {
  z-index: 9999;
}
