const route: App.I18n.Schema['translation']['route'] = {
  '(base)_home': '首页',
  '(base)_manage': '系统管理',
  '(base)_manage_role': '角色管理',
  '(base)_manage_role_[...slug]': '角色管理详情',
  '(base)_manage_user': '用户管理',
  '(base)_manage_user_[id]': '用户详情',
  '(base)_user-center': '个人中心',
  '(blank)_login': '登录',
  '(blank)_login_code-login': '验证码登录',
  '(blank)_login_register': ' 注册账号',
  '(blank)_login_reset-pwd': '重置密码',
  '(blank)_login-out': '退出登录',
  '403': '无权限',
  '404': '页面不存在',
  '500': '服务器错误',
  document: '文档',
  document_antd: 'Ant Design 文档',
  document_procomponents: 'ProComponents 文档',
  document_project: '项目文档',
  'document_project-link': '项目文档(外链)',
  document_react: 'React文档',
  document_ui: 'UI',
  document_unocss: 'UnoCSS文档',
  document_vite: 'Vite文档',
  exception: '异常页',
  exception_403: '403',
  exception_404: '404',
  exception_500: '500',
  'iframe-page': '外链页面',
  notFound: '页面不存在',
  root: '首页'
};

export default route;
