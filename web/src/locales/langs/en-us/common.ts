const common: App.I18n.Schema['translation']['common'] = {
  action: 'Action',
  add: 'Add',
  addSuccess: 'Add Success',
  backToHome: 'Back to home',
  batchDelete: 'Batch Delete',
  cancel: 'Cancel',
  check: 'Check',
  close: 'Close',
  columnSetting: 'Column Setting',
  config: 'Config',
  confirm: 'Confirm',
  confirmDelete: 'Are you sure you want to delete?',
  delete: 'Delete',
  deleteSuccess: 'Delete Success',
  edit: 'Edit',
  error: 'Error',
  errorHint: 'Please try again later',
  expandColumn: 'Expand Column',
  index: 'Index',
  keywordSearch: 'Please enter keyword',
  logout: 'Logout',
  logoutConfirm: 'Are you sure you want to log out?',
  lookForward: 'Coming soon',
  modify: 'Modify',
  modifySuccess: 'Modify Success',
  noData: 'No Data',
  operate: 'Operate',
  pleaseCheckValue: 'Please check whether the value is valid',
  refresh: 'Refresh',
  reset: 'Reset',
  search: 'Search',
  switch: 'Switch',
  tip: 'Tip',
  trigger: 'Trigger',
  tryAlign: 'Try Align',
  update: 'Update',
  updateSuccess: 'Update Success',
  userCenter: 'User Center',
  warning: 'Warning',
  yesOrNo: {
    no: 'No',
    yes: 'Yes'
  }
};

export default common;
