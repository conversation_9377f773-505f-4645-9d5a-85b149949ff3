const route: App.I18n.Schema['translation']['route'] = {
  '(base)_about': 'About',
  '(base)_function': 'System Function',
  '(base)_function_event-bus': 'Event Bus Demo',
  '(base)_function_hide-child': 'Hide Child',
  '(base)_function_hide-child_one': 'Hide Child',
  '(base)_function_hide-child_three': 'Three',
  '(base)_function_hide-child_two': 'Two',
  '(base)_function_multi-tab': 'Multi Tab',
  '(base)_function_request': 'Request',
  '(base)_function_super-page': 'Super Admin Visible',
  '(base)_function_tab': 'Tab',
  '(base)_function_toggle-auth': 'Toggle Auth',
  '(base)_function_use-request': 'useRequest Demo',
  '(base)_home': 'Home',
  '(base)_manage': 'System Manage',
  '(base)_manage_role': 'Role Manage',
  '(base)_manage_role_[...slug]': 'Role Manage Details',
  '(base)_manage_user': 'User Manage',
  '(base)_manage_user_[id]': 'User Detail',
  '(base)_multi-menu': 'Multi Menu',
  '(base)_multi-menu_first': 'Menu One',
  '(base)_multi-menu_first_child': 'Menu One Child',
  '(base)_multi-menu_second': 'Menu Two',
  '(base)_multi-menu_second_child': 'Menu Two Child',
  '(base)_multi-menu_second_child_home': 'Menu Two Child Home',
  '(base)_projects': 'Multi-level Dynamic Route',
  '(base)_projects_[pid]': 'Multi-level Dynamic Route Details',
  '(base)_projects_[pid]_edit': 'Multi-level Dynamic Route Edit',
  '(base)_projects_[pid]_edit_[id]': 'Multi-level Dynamic Route Edit Details',
  '(base)_user-center': 'User Center',
  '(blank)_login': 'Login',
  '(blank)_login_code-login': 'Code Login',
  '(blank)_login_register': 'Register Account',
  '(blank)_login_reset-pwd': 'Reset Password',
  '(blank)_login-out': 'Login Out',
  '403': 'No Permission',
  '404': 'Page Not Found',
  '500': 'Server Error',
  document: 'Document',
  document_antd: 'Ant Design  Document',
  document_procomponents: 'ProComponents Document',
  document_project: 'Project Document',
  'document_project-link': 'Project Document(External Link)',
  document_react: 'React Document',
  document_ui: 'UI',
  document_unocss: 'UnoCSS Document',
  document_vite: 'Vite Document',
  exception: 'Exception',
  exception_403: '403',
  exception_404: '404',
  exception_500: '500',
  'iframe-page': 'Iframe',
  notFound: 'Page Not Found',
  root: 'Home'
};

export default route;
