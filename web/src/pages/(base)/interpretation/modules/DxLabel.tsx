import React from 'react'



type DxLabelProps = {
  feature: Record<string, any>
  txtColor: string;
  bgColor: string;
}

const DxLabel = memo((props: DxLabelProps) => {


  return (
    <>
      <ASpace size={[8, 16]} direction="vertical">
        <ATag color={props.txtColor}>甲状腺弥漫性病变</ATag>
        <ATag color={props.bgColor} style={{ color: props.txtColor }}>【弥漫性病变】回声均匀性：不均匀</ATag>
      </ASpace>

    </>
  )
});

export default DxLabel;
