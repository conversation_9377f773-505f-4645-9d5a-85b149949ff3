import React from 'react'

const extractLeafValues = (data: Record<string, any>): string[] => {
  const values: string[] = [];

  if (data === null || data === undefined) {
    return values; // 返回空数组
  }

  const walk = (node: any): void => {
    if (node && typeof node === "object" && !Array.isArray(node)) {
      for (const key in node) {
        if (Object.prototype.hasOwnProperty.call(node, key)) {
          walk(node[key]);
        }
      }
    } else if (Array.isArray(node)) {
      for (const item of node) {
        walk(item);
      }
    } else {
      if (typeof node === "string") {
        values.push(node);
      } else if (node !== null && node !== undefined) {
        values.push(String(node));
      }
    }
  };
  walk(data);
  return values;
}

type LeafKV = {
  path: string;
  value: any[];  // value一定是数组
};

const extractLeafKV = (feature: Record<string, any>): LeafKV[] => {
  const values: LeafKV[] = [];

  const walk = (node: any, path = ""): void => {
    if (node && typeof node === "object" && !Array.isArray(node)) {
      for (const key in node) {
        if (Object.prototype.hasOwnProperty.call(node, key)) {
          const newPath = path ? `${path}.${key}` : key;
          walk(node[key], newPath);
        }
      }
    } else if (Array.isArray(node)) {
      // 如果是数组，直接放入数组里作为叶节点的值数组
      values.push({
        path,
        value: node,
      });
    } else {
      // 普通叶节点，包成单元素数组
      values.push({
        path,
        value: [node],
      });
    }
  };

  walk(feature);
  return values;
};

type ImagingFeatureProps = {
  feature: Record<string, any>
  txtColor: string;
  bgColor: string;
}

const ImagingFeature = memo((props: ImagingFeatureProps) => {

  const feature = props.feature;
  const structures = extractLeafValues(feature.structure)
  let diffuse_changes: LeafKV[] = [];
  if (feature.diffuse_changes?.length > 0) {
    diffuse_changes = extractLeafKV(feature.diffuse_changes[0])
  }
  let focal_lesions: LeafKV[][] = [];
  for (const item of feature.focal_lesions ?? []) {
    focal_lesions.push(extractLeafKV(item))
  }

  return (
    <>
      <ADivider orientation="left">解剖实体</ADivider>
      <ASpace size={[8, 16]} wrap>
        {structures.map((item, index) => (
          <ATag
            key={index}
            bordered={false}
            color={props.bgColor}
            style={{ color: props.txtColor }}
          >{item}</ATag>
        ))}
      </ASpace>
      <ADivider orientation="left" className="pt-3">弥漫性改变</ADivider>
      {diffuse_changes.map((item, index) => (
        <ARow key={index} gutter={16} className="pb-4">
          <ACol span={6}>
            <span className="text-gray-500">{item.path}:</span>
          </ACol>
          <ACol span={18}>
            {item.value.map((v, index) => (
              <ATag
                key={index}
                bordered={false}
                color={props.bgColor}
                style={{ color: props.txtColor }}
              >{v}</ATag>
            ))}
          </ACol>
        </ARow>
      ))}
      {focal_lesions.map((lesion, index) => (
        <div key={index}>
          <ADivider orientation="left">局灶性病灶 #{index + 1}</ADivider>
          {lesion.map((item, index) => (
            <ARow key={index} gutter={16} className="pb-4">
              <ACol span={6}>
                <span className="text-gray-500">{item.path}:</span>
              </ACol>
              <ACol span={18}>
                {item.value.map((v, index) => (
                  <ATag
                    key={index}
                    bordered={false}
                    color={props.bgColor}
                    style={{ color: props.txtColor }}
                  >{v}</ATag>
                ))}
              </ACol>
            </ARow>
          ))}
        </div>
      ))}
    </>
  )
});

export default ImagingFeature;
