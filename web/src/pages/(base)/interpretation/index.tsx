import useToken from 'antd/es/theme/useToken';
import React from 'react';

import DxLabel from '@/pages/(base)/interpretation/modules/DxLabel.tsx';
import ImagingFeature from '@/pages/(base)/interpretation/modules/ImagingFeature.tsx';
import { fetchAnnotate } from '@/service/api/imaging.ts';



const Interpretation = () => {

  const [, token] = useToken();

  const [loading, setLoading] = useState(false);
  const [reportText, setReportText] = useState('');
  const [featureData, setFeatureData] = useState({
        "structure": {
            "甲状腺本体": {
                "大小": "甲状腺大小正常",
                "回声特征": "甲状腺回声正常",
                "切面形态": "甲状腺切面形态正常",
                "光滑度": "甲状腺表面光滑"
            },
            "包膜": {
                "完整性": "甲状腺包膜清晰完整"
            },
            "血管": {
                "走行": "甲状腺血管走行清晰"
            },
            "周围淋巴结": {
                "可见度": "甲状腺周围未见明显形态饱满淋巴结"
            }
        },
        "diffuse_changes": [{
            "回声均匀性": "不均匀",
            "回声纹理特征": [
                "回声光点细密"
            ]
        }],
        "focal_lesions": [
            {
                "发现部位": "甲状腺左叶",
                "回声强度": "低回声",
                "回声分布": "局限性",
                "回声均匀性": "不均匀",
                "内部成分": [
                    "实性"
                ],
                "回声纹理特征": [
                    "回声光点细密"
                ],
                "边缘特征": [
                    "清晰",
                    "光滑"
                ],
                "后方回声特征": [
                    "无改变"
                ],
                "血流信号": "无血流信号",
                "大小": "小",
                "形状": "圆形"
            },
            {
                "发现部位": "甲状腺右叶",
                "回声强度": "低回声",
                "回声分布": "局限性",
                "回声均匀性": "不均匀",
                "内部成分": [
                    "实性"
                ],
                "回声纹理特征": [
                    "回声光点细密"
                ],
                "边缘特征": [
                    "清晰",
                    "光滑"
                ],
                "后方回声特征": [
                    "无改变"
                ],
                "血流信号": "无血流信号",
                "大小": "中",
                "形状": "圆形"
            }
        ]
    });

  const isAnalysisDisabled = useMemo(() => !reportText.trim(), [reportText]);

  const handleAnalysis = async () => {
    setLoading(true);
    const { data } = await fetchAnnotate(reportText);
    setFeatureData(data);
    setLoading(false);
  };

  return (
    <AFlex
      gap={12}
      vertical={false}
    >
      <ACard
        title="影像学检查所见"
        className="mb-4 flex-1"
        size="small"
        variant="borderless"
      >
        <AInput.TextArea
          placeholder="请输入"
          value={reportText}
          onChange={e => {
            setReportText(e.target.value);
          }}
          autoSize={{ minRows: 6, maxRows: 24 }}
        />
        <div className="mt-3 flex justify-end">
          <AButton
            type="primary"
            htmlType="submit"
            disabled={isAnalysisDisabled}
            loading={loading}
            onClick={handleAnalysis}
          >
            分析
          </AButton>
        </div>
      </ACard>
      <ACard
        title="影像学标签"
        className="mb-4 flex-1"
        size="small"
        variant="borderless"
        loading={loading}
      >
        {featureData ? (
          <ImagingFeature
            feature={featureData}
            txtColor={token.colorPrimary}
            bgColor={token.colorPrimaryBgHover}
          />
        ) : (
          <div className="text-gray-500">请先分析影像学报告</div>
        )}
      </ACard>
      <ACard
        title="拟诊标签"
        size="small"
        className="mb-4 flex-1"
        variant="borderless"
      >
        <DxLabel
          feature={featureData}
          txtColor={token.colorPrimary}
          bgColor={token.colorPrimaryBgHover}
        />
      </ACard>
    </AFlex>
  );
};

export default Interpretation;
