/* prettier-ignore */
/* eslint-disable */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router
// 请不要手动修改此文件，否则会导致优雅路由无法正常工作
// 如果需要修改，请在优雅路由配置文件中进行修改
// 这是自动生成的文件，请不要手动修改



export const layouts: Record<string, () => Promise<any>> = {
"(base)": () => import("@/pages/(base)/layout.tsx"),
"(blank)": () => import("@/pages/(blank)/layout.tsx"),
"(blank)_login": () => import("@/pages/(blank)/login/layout.tsx"),
"root": () => import("@/pages/layout.tsx"),
};

export const pages: Record<string, () => Promise<any>> = {
"(base)_home": () => import("@/pages/(base)/home/<USER>"),
"(base)_inference": () => import("@/pages/(base)/inference/index.tsx"),
"(base)_interpretation": () => import("@/pages/(base)/interpretation/index.tsx"),
"(base)_knowledge": () => import("@/pages/(base)/knowledge/index.tsx"),
"(base)_user-center": () => import("@/pages/(base)/user-center/index.tsx"),
"(blank)_login-out": () => import("@/pages/(blank)/login-out/index.tsx"),
"(blank)_login_code-login": () => import("@/pages/(blank)/login/code-login/index.tsx"),
"(blank)_login": () => import("@/pages/(blank)/login/index.tsx"),
"(blank)_login_register": () => import("@/pages/(blank)/login/register/index.tsx"),
"(blank)_login_reset-pwd": () => import("@/pages/(blank)/login/reset-pwd/index.tsx"),
"403": () => import("@/pages/_builtin/403/index.tsx"),
"404": () => import("@/pages/_builtin/404/index.tsx"),
"500": () => import("@/pages/_builtin/500/index.tsx"),
"iframe-page": () => import("@/pages/_builtin/iframe-page/index.tsx"),
"root": () => import("@/pages/index.tsx"),
};

export const errors: Record<string, () => Promise<any>> = {
"root": () => import("@/pages/error.tsx"),
};