/* prettier-ignore */
/* eslint-disable */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router
// 请不要手动修改此文件，否则会导致优雅路由无法正常工作
// 如果需要修改，请在优雅路由配置文件中进行修改
// 这是自动生成的文件，请不要手动修改


import type { RouteKey, RouteMap, RoutePath } from '@soybean-react/vite-plugin-react-router';

/**
* map of route name and route path
*/
export const routeMap: RouteMap = {
 "not-found": "*",
 "exception": "/exception",
 "exception_403": "/exception/403",
 "exception_404": "/exception/404",
 "exception_500": "/exception/500",
 "document": "/document",
 "document_project": "/document/project",
 "document_project-link": "/document/project-link",
 "document_react": "/document/react",
 "document_vite": "/document/vite",
 "document_unocss": "/document/unocss",
 "document_procomponents": "/document/procomponents",
 "document_antd": "/document/antd",
 "document_ui": "/document/ui",
 "(base)_home": "/home",
 "(base)_inference": "/inference",
 "(base)_interpretation": "/interpretation",
 "(base)_knowledge": "/knowledge",
 "(base)_user-center": "/user-center",
 "(blank)_login": "/login",
 "(blank)_login_code-login": "/login/code-login",
 "(blank)_login_register": "/login/register",
 "(blank)_login_reset-pwd": "/login/reset-pwd",
 "(blank)_login-out": "/login-out",
 "403": "/403",
 "404": "/404",
 "500": "/500",
 "iframe-page": "/iframe-page",
 "root": "/"
};

/**
* get route path by route name
*
* @param name route name
*/
export function getRoutePath<T extends RouteKey>(name: T) {
 return routeMap[name];
}

/**
* get route name by route path
*
* @param path route path
*/
export function getRouteName(path: RoutePath) {
 const routeEntries = Object.entries(routeMap) as [RouteKey, RoutePath][];

 const routeName: RouteKey | null = routeEntries.find(([, routePath]) => routePath === path)?.[0] || null;

 return routeName;
}